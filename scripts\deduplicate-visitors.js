// Script to deduplicate visitors
// Run with: node scripts/deduplicate-visitors.js

const fetch = require('node-fetch');

async function deduplicateVisitors() {
  try {
    console.log('Starting visitor deduplication process...');
    
    // Replace with your admin user ID and actual domain
    const userId = process.env.ADMIN_USER_ID || '67f2bb470ba03bb2e0912c3f';
    const domain = process.env.DOMAIN || 'http://localhost:3000';
    
    // Call the deduplication endpoint
    const response = await fetch(`${domain}/api/admin/visitors/deduplicate?userId=${userId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to deduplicate visitors: ${errorData.error || response.statusText}`);
    }
    
    const result = await response.json();
    console.log('Deduplication completed successfully!');
    console.log(`Removed ${result.removedCount} duplicate records`);
    console.log(`Merged ${result.mergedCount} visitor records`);
    
    return result;
  } catch (error) {
    console.error('Error during deduplication:', error);
    process.exit(1);
  }
}

// Run the deduplication
deduplicateVisitors();
