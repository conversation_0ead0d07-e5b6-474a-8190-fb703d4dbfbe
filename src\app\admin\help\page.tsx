'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  MessageSquare,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Users,
  Star,
  Search,
  Filter,
  Eye,
  Calendar,
  BarChart3,
  MoreHorizontal,
  Edit,
  Trash2,
  UserPlus,
  CheckSquare,
  XCircle,
  AlertTriangle,
  FileText,
  RefreshCw
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';

interface HelpStats {
  overview: {
    totalTickets: number;
    openTickets: number;
    inProgressTickets: number;
    resolvedTickets: number;
    closedTickets: number;
    escalatedTickets: number;
    recentTickets: number;
    recentResolved: number;
    averageResolutionTime: number;
  };
  ticketsByCategory: Array<{
    _id: string;
    count: number;
    open: number;
    resolved: number;
  }>;
  ticketsByPriority: Array<{
    _id: string;
    count: number;
  }>;
  topAgents: Array<{
    _id: string;
    assignedToName: string;
    totalTickets: number;
    resolvedTickets: number;
  }>;
  satisfaction: {
    averageRating: number;
    totalRatings: number;
    distribution: Record<string, number>;
  };
}

interface Ticket {
  _id: string;
  ticketNumber: string;
  subject: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'waiting_for_user' | 'resolved' | 'closed';
  createdAt: string;
  updatedAt: string;
  userId: {
    name: string;
    email: string;
  };
  assignedTo?: {
    name: string;
  };
  responses: string[];
  escalated: boolean;
}

const statusConfig = {
  open: { label: 'Open', color: 'bg-blue-500/20 text-blue-400', icon: AlertCircle },
  in_progress: { label: 'In Progress', color: 'bg-yellow-500/20 text-yellow-400', icon: Clock },
  waiting_for_user: { label: 'Waiting for User', color: 'bg-orange-500/20 text-orange-400', icon: MessageSquare },
  resolved: { label: 'Resolved', color: 'bg-green-500/20 text-green-400', icon: CheckCircle },
  closed: { label: 'Closed', color: 'bg-gray-500/20 text-gray-400', icon: CheckCircle }
};

const priorityConfig = {
  low: { label: 'Low', color: 'bg-gray-500/20 text-gray-400' },
  medium: { label: 'Medium', color: 'bg-blue-500/20 text-blue-400' },
  high: { label: 'High', color: 'bg-orange-500/20 text-orange-400' },
  urgent: { label: 'Urgent', color: 'bg-red-500/20 text-red-400' }
};

export default function AdminHelpPage() {
  const { user } = useAuth();
  const [stats, setStats] = useState<HelpStats | null>(null);
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [loading, setLoading] = useState(true);
  const [ticketsLoading, setTicketsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedTickets, setSelectedTickets] = useState<string[]>([]);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [ticketToDelete, setTicketToDelete] = useState<string | null>(null);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [ticketToUpdate, setTicketToUpdate] = useState<string | null>(null);
  const [newStatus, setNewStatus] = useState<string>('');
  const [resolutionNotes, setResolutionNotes] = useState('');
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);
  const [assigneeId, setAssigneeId] = useState('');
  const [adminUsers, setAdminUsers] = useState<Array<{id: string, name: string}>>([]);
  const [assignedFilter, setAssignedFilter] = useState<string>('all');
  const [escalatedFilter, setEscalatedFilter] = useState<string>('all');
  const [dateFromFilter, setDateFromFilter] = useState<string>('');
  const [dateToFilter, setDateToFilter] = useState<string>('');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] = useState(false);

  useEffect(() => {
    fetchStats();
    fetchTickets();
    fetchAdminUsers();
  }, []);

  useEffect(() => {
    fetchTickets();
  }, [page, statusFilter, priorityFilter, searchQuery, assignedFilter, escalatedFilter, dateFromFilter, dateToFilter]);

  const fetchStats = async () => {
    try {
      const response = await fetch(`/api/help/stats?userId=${user?.id}`, {
        credentials: 'include' // Include cookies in the request
      });
      if (!response.ok) throw new Error('Failed to fetch stats');
      const data = await response.json();
      setStats(data);
    } catch (error) {
      console.error('Error fetching help stats:', error);
      toast.error('Failed to load statistics');
    }
  };

  const fetchTickets = async () => {
    try {
      setTicketsLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20',
        userId: user?.id || '' // Use the actual admin user ID
      });

      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (priorityFilter !== 'all') params.append('priority', priorityFilter);
      if (searchQuery) params.append('search', searchQuery);
      if (assignedFilter !== 'all') params.append('assigned', assignedFilter);
      if (escalatedFilter !== 'all') params.append('escalated', escalatedFilter);
      if (dateFromFilter) params.append('dateFrom', dateFromFilter);
      if (dateToFilter) params.append('dateTo', dateToFilter);

      const response = await fetch(`/api/help/tickets?${params}`, {
        credentials: 'include' // Include cookies in the request
      });
      if (!response.ok) throw new Error('Failed to fetch tickets');

      const data = await response.json();
      setTickets(data.tickets);
      setTotalPages(data.pagination.pages);
    } catch (error) {
      console.error('Error fetching tickets:', error);
      toast.error('Failed to load tickets');
    } finally {
      setTicketsLoading(false);
      setLoading(false);
    }
  };

  const fetchAdminUsers = async () => {
    try {
      const response = await fetch(`/api/admin/users?role=admin&userId=${user?.id}`, {
        credentials: 'include'
      });
      if (response.ok) {
        const data = await response.json();
        setAdminUsers(data.users?.map((u: any) => ({ id: u._id, name: u.name })) || []);
      }
    } catch (error) {
      console.error('Error fetching admin users:', error);
    }
  };

  const handleTicketAction = async (ticketId: string, action: string, data?: any) => {
    try {
      const response = await fetch(`/api/help/tickets/${ticketId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          userId: user?.id,
          ...data
        }),
      });

      if (!response.ok) throw new Error('Failed to update ticket');

      toast.success(`Ticket ${action} successfully`);
      fetchTickets();
      fetchStats();
    } catch (error) {
      console.error(`Error ${action} ticket:`, error);
      toast.error(`Failed to ${action} ticket`);
    }
  };

  const handleDeleteTicket = async (ticketId: string) => {
    try {
      const response = await fetch(`/api/help/tickets/${ticketId}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) throw new Error('Failed to delete ticket');

      toast.success('Ticket deleted successfully');
      fetchTickets();
      fetchStats();
      setIsDeleteDialogOpen(false);
      setTicketToDelete(null);
    } catch (error) {
      console.error('Error deleting ticket:', error);
      toast.error('Failed to delete ticket');
    }
  };

  const handleBulkAction = async (action: string, data?: any) => {
    if (selectedTickets.length === 0) {
      toast.error('Please select tickets first');
      return;
    }

    try {
      let response;

      if (action === 'delete') {
        // For delete action, we need to call each ticket's delete endpoint
        const deletePromises = selectedTickets.map(ticketId =>
          fetch(`/api/help/tickets/${ticketId}`, {
            method: 'DELETE',
            credentials: 'include',
          })
        );

        const responses = await Promise.all(deletePromises);
        const failedDeletes = responses.filter(r => !r.ok);

        if (failedDeletes.length > 0) {
          throw new Error(`Failed to delete ${failedDeletes.length} tickets`);
        }

        toast.success(`${selectedTickets.length} ticket${selectedTickets.length > 1 ? 's' : ''} deleted successfully`);
      } else {
        // For other actions, use the bulk update endpoint
        response = await fetch('/api/help/tickets', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            userId: user?.id,
            ticketIds: selectedTickets,
            action,
            ...data
          }),
        });

        if (!response.ok) throw new Error(`Failed to ${action} tickets`);

        const actionPastTense = action === 'resolve' ? 'resolved' : action === 'close' ? 'closed' : action + 'd';
        toast.success(`${selectedTickets.length} ticket${selectedTickets.length > 1 ? 's' : ''} ${actionPastTense} successfully`);
      }

      setSelectedTickets([]);
      fetchTickets();
      fetchStats();
    } catch (error) {
      console.error(`Error ${action} tickets:`, error);
      toast.error(`Failed to ${action} tickets`);
    }
  };

  const handleBulkDelete = () => {
    setIsBulkDeleteDialogOpen(false);
    handleBulkAction('delete');
  };

  const handleSelectTicket = (ticketId: string) => {
    setSelectedTickets(prev =>
      prev.includes(ticketId)
        ? prev.filter(id => id !== ticketId)
        : [...prev, ticketId]
    );
  };

  const handleSelectAll = () => {
    if (selectedTickets.length === tickets.length) {
      setSelectedTickets([]);
    } else {
      setSelectedTickets(tickets.map(t => t._id));
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-vista-light/20 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-vista-light/20 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-vista-light/20 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-vista-light">Help Center Management</h1>
          <p className="text-vista-light/70">Manage support tickets and view analytics</p>
        </div>
        <Button className="bg-vista-blue hover:bg-vista-blue/90">
          <MessageSquare className="w-4 h-4 mr-2" />
          View All Tickets
        </Button>
      </div>

      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-vista-card border-vista-light/10">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-vista-light/70 text-sm">Total Tickets</p>
                  <p className="text-2xl font-bold text-vista-light">{stats.overview.totalTickets}</p>
                </div>
                <MessageSquare className="w-8 h-8 text-vista-blue" />
              </div>
              <div className="mt-2 text-sm text-vista-light/60">
                +{stats.overview.recentTickets} this month
              </div>
            </CardContent>
          </Card>

          <Card className="bg-vista-card border-vista-light/10">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-vista-light/70 text-sm">Open Tickets</p>
                  <p className="text-2xl font-bold text-vista-light">{stats.overview.openTickets}</p>
                </div>
                <AlertCircle className="w-8 h-8 text-orange-400" />
              </div>
              <div className="mt-2 text-sm text-vista-light/60">
                {stats.overview.inProgressTickets} in progress
              </div>
            </CardContent>
          </Card>

          <Card className="bg-vista-card border-vista-light/10">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-vista-light/70 text-sm">Avg Resolution</p>
                  <p className="text-2xl font-bold text-vista-light">{stats.overview.averageResolutionTime}h</p>
                </div>
                <Clock className="w-8 h-8 text-yellow-400" />
              </div>
              <div className="mt-2 text-sm text-vista-light/60">
                {stats.overview.recentResolved} resolved this month
              </div>
            </CardContent>
          </Card>

          <Card className="bg-vista-card border-vista-light/10">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-vista-light/70 text-sm">Satisfaction</p>
                  <p className="text-2xl font-bold text-vista-light">{stats.satisfaction.averageRating}/5</p>
                </div>
                <Star className="w-8 h-8 text-yellow-400" />
              </div>
              <div className="mt-2 text-sm text-vista-light/60">
                {stats.satisfaction.totalRatings} ratings
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs defaultValue="tickets" className="space-y-6">
        <TabsList className="bg-vista-card border-vista-light/10">
          <TabsTrigger value="tickets" className="data-[state=active]:bg-vista-blue data-[state=active]:text-white">
            Tickets
          </TabsTrigger>
          <TabsTrigger value="analytics" className="data-[state=active]:bg-vista-blue data-[state=active]:text-white">
            Analytics
          </TabsTrigger>
          <TabsTrigger value="categories" className="data-[state=active]:bg-vista-blue data-[state=active]:text-white">
            Categories
          </TabsTrigger>
        </TabsList>

        <TabsContent value="tickets" className="space-y-6">
          {/* Filters and Bulk Actions */}
          <Card className="bg-vista-card border-vista-light/10">
            <CardContent className="p-6 space-y-4">
              {/* Basic Filters */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-vista-light/60 w-4 h-4" />
                  <Input
                    placeholder="Search tickets..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 bg-vista-dark border-vista-light/20 text-vista-light"
                  />
                </div>

                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="bg-vista-dark border-vista-light/20 text-vista-light">
                    <SelectValue placeholder="All Statuses" />
                  </SelectTrigger>
                  <SelectContent className="bg-vista-dark border-vista-light/20">
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="open">Open</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="waiting_for_user">Waiting for User</SelectItem>
                    <SelectItem value="resolved">Resolved</SelectItem>
                    <SelectItem value="closed">Closed</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                  <SelectTrigger className="bg-vista-dark border-vista-light/20 text-vista-light">
                    <SelectValue placeholder="All Priorities" />
                  </SelectTrigger>
                  <SelectContent className="bg-vista-dark border-vista-light/20">
                    <SelectItem value="all">All Priorities</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                    className="border-vista-light/20 text-vista-light hover:bg-vista-light/10"
                  >
                    <Filter className="w-4 h-4 mr-2" />
                    {showAdvancedFilters ? 'Hide' : 'More'} Filters
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearchQuery('');
                      setStatusFilter('all');
                      setPriorityFilter('all');
                      setAssignedFilter('all');
                      setEscalatedFilter('all');
                      setDateFromFilter('');
                      setDateToFilter('');
                    }}
                    className="border-vista-light/20 text-vista-light hover:bg-vista-light/10"
                  >
                    Clear All
                  </Button>
                </div>
              </div>

              {/* Advanced Filters */}
              {showAdvancedFilters && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 pt-4 border-t border-vista-light/10">
                  <Select value={assignedFilter} onValueChange={setAssignedFilter}>
                    <SelectTrigger className="bg-vista-dark border-vista-light/20 text-vista-light">
                      <SelectValue placeholder="All Assignments" />
                    </SelectTrigger>
                    <SelectContent className="bg-vista-dark border-vista-light/20">
                      <SelectItem value="all">All Assignments</SelectItem>
                      <SelectItem value="unassigned">Unassigned</SelectItem>
                      <SelectItem value="assigned">Assigned</SelectItem>
                      {adminUsers.map((admin) => (
                        <SelectItem key={admin.id} value={admin.id}>
                          {admin.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select value={escalatedFilter} onValueChange={setEscalatedFilter}>
                    <SelectTrigger className="bg-vista-dark border-vista-light/20 text-vista-light">
                      <SelectValue placeholder="Escalation Status" />
                    </SelectTrigger>
                    <SelectContent className="bg-vista-dark border-vista-light/20">
                      <SelectItem value="all">All Tickets</SelectItem>
                      <SelectItem value="escalated">Escalated Only</SelectItem>
                      <SelectItem value="not_escalated">Not Escalated</SelectItem>
                    </SelectContent>
                  </Select>

                  <div>
                    <Label className="text-vista-light text-xs">From Date</Label>
                    <Input
                      type="date"
                      value={dateFromFilter}
                      onChange={(e) => setDateFromFilter(e.target.value)}
                      className="bg-vista-dark border-vista-light/20 text-vista-light mt-1"
                    />
                  </div>

                  <div>
                    <Label className="text-vista-light text-xs">To Date</Label>
                    <Input
                      type="date"
                      value={dateToFilter}
                      onChange={(e) => setDateToFilter(e.target.value)}
                      className="bg-vista-dark border-vista-light/20 text-vista-light mt-1"
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="bg-vista-card border-vista-light/10">
            <CardContent className="p-4">
              <div className="flex flex-wrap gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setStatusFilter('open');
                    setShowAdvancedFilters(false);
                  }}
                  className="border-blue-500/30 text-blue-400 hover:bg-blue-500/10"
                >
                  <AlertCircle className="w-4 h-4 mr-1" />
                  Open Tickets
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setStatusFilter('resolved');
                    setShowAdvancedFilters(false);
                  }}
                  className="border-green-500/30 text-green-400 hover:bg-green-500/10"
                >
                  <CheckCircle className="w-4 h-4 mr-1" />
                  Resolved Tickets
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setPriorityFilter('urgent');
                    setShowAdvancedFilters(false);
                  }}
                  className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                >
                  <AlertTriangle className="w-4 h-4 mr-1" />
                  Urgent Priority
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setEscalatedFilter('escalated');
                    setShowAdvancedFilters(true);
                  }}
                  className="border-orange-500/30 text-orange-400 hover:bg-orange-500/10"
                >
                  <AlertTriangle className="w-4 h-4 mr-1" />
                  Escalated
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setAssignedFilter('unassigned');
                    setShowAdvancedFilters(true);
                  }}
                  className="border-yellow-500/30 text-yellow-400 hover:bg-yellow-500/10"
                >
                  <UserPlus className="w-4 h-4 mr-1" />
                  Unassigned
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Bulk Actions Bar */}
          {selectedTickets.length > 0 && (
            <Card className="bg-vista-blue/10 border-vista-blue/20">
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <span className="text-vista-light text-sm">
                    {selectedTickets.length} ticket{selectedTickets.length > 1 ? 's' : ''} selected
                  </span>
                  <div className="flex gap-2 ml-auto">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleBulkAction('resolve', { status: 'resolved', resolvedAt: new Date(), resolvedBy: user?.id })}
                      className="border-green-500/30 text-green-400 hover:bg-green-500/10"
                    >
                      <CheckCircle className="w-4 h-4 mr-1" />
                      Resolve
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleBulkAction('close', { status: 'closed' })}
                      className="border-gray-500/30 text-gray-400 hover:bg-gray-500/10"
                    >
                      <XCircle className="w-4 h-4 mr-1" />
                      Close
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setIsBulkDeleteDialogOpen(true)}
                      className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                    >
                      <Trash2 className="w-4 h-4 mr-1" />
                      Delete
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => setSelectedTickets([])}
                      className="text-vista-light/70 hover:text-vista-light"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}



          {/* Tickets List */}
          <Card className="bg-vista-card border-vista-light/10">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-vista-light">Support Tickets</CardTitle>
                {tickets.length > 0 && (
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleSelectAll}
                      className="border-vista-light/20 text-vista-light hover:bg-vista-light/10"
                    >
                      {selectedTickets.length === tickets.length ? 'Deselect All' : 'Select All'}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={fetchTickets}
                      className="border-vista-light/20 text-vista-light hover:bg-vista-light/10"
                    >
                      <RefreshCw className="w-4 h-4 mr-1" />
                      Refresh
                    </Button>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {ticketsLoading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-4 bg-vista-light/20 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-vista-light/20 rounded w-1/2"></div>
                    </div>
                  ))}
                </div>
              ) : tickets.length > 0 ? (
                <div className="space-y-4">
                  {tickets.map((ticket) => {
                    const StatusIcon = statusConfig[ticket.status].icon;
                    const isSelected = selectedTickets.includes(ticket._id);
                    return (
                      <div key={ticket._id} className={`p-4 border rounded-lg transition-colors ${
                        isSelected
                          ? 'border-vista-blue/50 bg-vista-blue/5'
                          : 'border-vista-light/10 hover:border-vista-blue/30'
                      }`}>
                        <div className="flex items-start gap-4">
                          {/* Checkbox */}
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={() => handleSelectTicket(ticket._id)}
                            className="mt-1 w-4 h-4 text-vista-blue bg-vista-dark border-vista-light/20 rounded focus:ring-vista-blue focus:ring-2"
                          />

                          {/* Ticket Content */}
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <Link href={`/admin/help/tickets/${ticket._id}`}>
                                <h3 className="font-semibold text-vista-light hover:text-vista-blue transition-colors cursor-pointer">
                                  {ticket.subject}
                                </h3>
                              </Link>
                              <Badge variant="outline" className="border-vista-light/20 text-vista-light/70 text-xs">
                                #{ticket.ticketNumber}
                              </Badge>
                              {ticket.escalated && (
                                <Badge className="bg-red-500/20 text-red-400 text-xs">
                                  Escalated
                                </Badge>
                              )}
                            </div>

                            <div className="flex flex-wrap items-center gap-2 mb-2">
                              <Badge className={statusConfig[ticket.status].color}>
                                <StatusIcon className="w-3 h-3 mr-1" />
                                {statusConfig[ticket.status].label}
                              </Badge>
                              <Badge className={priorityConfig[ticket.priority].color}>
                                {priorityConfig[ticket.priority].label}
                              </Badge>
                              <Badge variant="outline" className="border-vista-light/20 text-vista-light/60 text-xs">
                                {ticket.category.replace('_', ' ')}
                              </Badge>
                            </div>

                            <div className="flex items-center gap-4 text-sm text-vista-light/60">
                              <span>{ticket.userId.name} ({ticket.userId.email})</span>
                              <span>•</span>
                              <span>{formatDate(ticket.createdAt)}</span>
                              {ticket.assignedTo && (
                                <>
                                  <span>•</span>
                                  <span>Assigned to {ticket.assignedTo.name}</span>
                                </>
                              )}
                              <span>•</span>
                              <span>{ticket.responses.length} responses</span>
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex items-center gap-2">
                            <Link href={`/admin/help/tickets/${ticket._id}`}>
                              <Button variant="ghost" size="sm" className="text-vista-light/60 hover:text-vista-light">
                                <Eye className="w-4 h-4" />
                              </Button>
                            </Link>

                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="text-vista-light/60 hover:text-vista-light">
                                  <MoreHorizontal className="w-4 h-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent className="bg-vista-dark border-vista-light/20">
                                {ticket.status !== 'resolved' && (
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setTicketToUpdate(ticket._id);
                                      setNewStatus('resolved');
                                      setIsStatusDialogOpen(true);
                                    }}
                                    className="text-green-400 hover:bg-green-500/10"
                                  >
                                    <CheckCircle className="w-4 h-4 mr-2" />
                                    Mark as Resolved
                                  </DropdownMenuItem>
                                )}

                                {ticket.status !== 'in_progress' && (
                                  <DropdownMenuItem
                                    onClick={() => handleTicketAction(ticket._id, 'updated', { status: 'in_progress' })}
                                    className="text-yellow-400 hover:bg-yellow-500/10"
                                  >
                                    <Clock className="w-4 h-4 mr-2" />
                                    Mark as In Progress
                                  </DropdownMenuItem>
                                )}

                                {ticket.status !== 'closed' && (
                                  <DropdownMenuItem
                                    onClick={() => handleTicketAction(ticket._id, 'updated', { status: 'closed' })}
                                    className="text-gray-400 hover:bg-gray-500/10"
                                  >
                                    <XCircle className="w-4 h-4 mr-2" />
                                    Close Ticket
                                  </DropdownMenuItem>
                                )}

                                <DropdownMenuItem
                                  onClick={() => {
                                    setTicketToUpdate(ticket._id);
                                    setIsAssignDialogOpen(true);
                                  }}
                                  className="text-vista-blue hover:bg-vista-blue/10"
                                >
                                  <UserPlus className="w-4 h-4 mr-2" />
                                  Assign Ticket
                                </DropdownMenuItem>

                                <DropdownMenuSeparator className="bg-vista-light/20" />

                                <DropdownMenuItem
                                  onClick={() => {
                                    setTicketToDelete(ticket._id);
                                    setIsDeleteDialogOpen(true);
                                  }}
                                  className="text-red-400 hover:bg-red-500/10"
                                >
                                  <Trash2 className="w-4 h-4 mr-2" />
                                  Delete Ticket
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      </div>
                    );
                  })}

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="flex justify-center gap-2 mt-6">
                      <Button
                        variant="outline"
                        onClick={() => setPage(p => Math.max(1, p - 1))}
                        disabled={page === 1}
                        className="border-vista-light/20 text-vista-light"
                      >
                        Previous
                      </Button>
                      <span className="flex items-center px-4 text-vista-light/70">
                        Page {page} of {totalPages}
                      </span>
                      <Button
                        variant="outline"
                        onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                        disabled={page === totalPages}
                        className="border-vista-light/20 text-vista-light"
                      >
                        Next
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <MessageSquare className="w-12 h-12 text-vista-light/40 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-vista-light mb-2">No tickets found</h3>
                  <p className="text-vista-light/70">
                    {searchQuery || statusFilter !== 'all' || priorityFilter !== 'all' 
                      ? 'Try adjusting your filters to see more results.'
                      : 'No support tickets have been created yet.'
                    }
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {stats && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Category Breakdown */}
              <Card className="bg-vista-card border-vista-light/10">
                <CardHeader>
                  <CardTitle className="text-vista-light flex items-center gap-2">
                    <BarChart3 className="w-5 h-5" />
                    Tickets by Category
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {stats.ticketsByCategory.map((category) => (
                      <div key={category._id} className="flex items-center justify-between">
                        <span className="text-vista-light capitalize">{category._id.replace('_', ' ')}</span>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="border-vista-light/20 text-vista-light/70">
                            {category.count} total
                          </Badge>
                          <Badge className="bg-green-500/20 text-green-400">
                            {category.resolved} resolved
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Top Agents */}
              <Card className="bg-vista-card border-vista-light/10">
                <CardHeader>
                  <CardTitle className="text-vista-light flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    Top Support Agents
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {stats.topAgents.slice(0, 5).map((agent) => (
                      <div key={agent._id} className="flex items-center justify-between">
                        <span className="text-vista-light">{agent.assignedToName}</span>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="border-vista-light/20 text-vista-light/70">
                            {agent.totalTickets} assigned
                          </Badge>
                          <Badge className="bg-green-500/20 text-green-400">
                            {agent.resolvedTickets} resolved
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="categories">
          <Card className="bg-vista-card border-vista-light/10">
            <CardHeader>
              <CardTitle className="text-vista-light">Manage Categories</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-vista-light/70">Category management coming soon...</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="bg-vista-dark border-vista-light/20">
          <DialogHeader>
            <DialogTitle className="text-vista-light">Delete Ticket</DialogTitle>
            <DialogDescription className="text-vista-light/70">
              Are you sure you want to delete this ticket? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              className="border-vista-light/20 text-vista-light"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => ticketToDelete && handleDeleteTicket(ticketToDelete)}
            >
              Delete Ticket
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Status Update Dialog */}
      <Dialog open={isStatusDialogOpen} onOpenChange={setIsStatusDialogOpen}>
        <DialogContent className="bg-vista-dark border-vista-light/20">
          <DialogHeader>
            <DialogTitle className="text-vista-light">Update Ticket Status</DialogTitle>
            <DialogDescription className="text-vista-light/70">
              {newStatus === 'resolved' ? 'Mark this ticket as resolved and add resolution notes.' : 'Update the ticket status.'}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {newStatus === 'resolved' && (
              <div>
                <Label htmlFor="resolution-notes" className="text-vista-light">Resolution Notes</Label>
                <Textarea
                  id="resolution-notes"
                  placeholder="Describe how this issue was resolved..."
                  value={resolutionNotes}
                  onChange={(e) => setResolutionNotes(e.target.value)}
                  className="bg-vista-dark border-vista-light/20 text-vista-light mt-2"
                />
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsStatusDialogOpen(false);
                setResolutionNotes('');
              }}
              className="border-vista-light/20 text-vista-light"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (ticketToUpdate) {
                  const updateData: any = { status: newStatus };
                  if (newStatus === 'resolved') {
                    updateData.resolvedAt = new Date();
                    updateData.resolvedBy = user?.id;
                    if (resolutionNotes.trim()) {
                      updateData.resolutionNotes = resolutionNotes.trim();
                    }
                  }
                  handleTicketAction(ticketToUpdate, 'updated', updateData);
                  setIsStatusDialogOpen(false);
                  setResolutionNotes('');
                  setTicketToUpdate(null);
                }
              }}
              className="bg-vista-blue hover:bg-vista-blue/90"
            >
              Update Status
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Assignment Dialog */}
      <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
        <DialogContent className="bg-vista-dark border-vista-light/20">
          <DialogHeader>
            <DialogTitle className="text-vista-light">Assign Ticket</DialogTitle>
            <DialogDescription className="text-vista-light/70">
              Assign this ticket to an admin user for handling.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="assignee" className="text-vista-light">Assign to</Label>
              <Select value={assigneeId} onValueChange={setAssigneeId}>
                <SelectTrigger className="bg-vista-dark border-vista-light/20 text-vista-light mt-2">
                  <SelectValue placeholder="Select an admin user" />
                </SelectTrigger>
                <SelectContent className="bg-vista-dark border-vista-light/20">
                  {adminUsers.map((admin) => (
                    <SelectItem key={admin.id} value={admin.id}>
                      {admin.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsAssignDialogOpen(false);
                setAssigneeId('');
              }}
              className="border-vista-light/20 text-vista-light"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (ticketToUpdate && assigneeId) {
                  const selectedAdmin = adminUsers.find(admin => admin.id === assigneeId);
                  handleTicketAction(ticketToUpdate, 'assigned', {
                    assignedTo: assigneeId,
                    assignedToName: selectedAdmin?.name
                  });
                  setIsAssignDialogOpen(false);
                  setAssigneeId('');
                  setTicketToUpdate(null);
                }
              }}
              disabled={!assigneeId}
              className="bg-vista-blue hover:bg-vista-blue/90"
            >
              Assign Ticket
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Delete Confirmation Dialog */}
      <Dialog open={isBulkDeleteDialogOpen} onOpenChange={setIsBulkDeleteDialogOpen}>
        <DialogContent className="bg-vista-dark border-vista-light/20">
          <DialogHeader>
            <DialogTitle className="text-vista-light">Delete Multiple Tickets</DialogTitle>
            <DialogDescription className="text-vista-light/70">
              Are you sure you want to delete {selectedTickets.length} ticket{selectedTickets.length > 1 ? 's' : ''}?
              This action cannot be undone and will permanently remove all ticket data including conversation history.
            </DialogDescription>
          </DialogHeader>
          <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4 my-4">
            <div className="flex items-center gap-2 text-red-400">
              <AlertTriangle className="w-5 h-5" />
              <span className="font-semibold">Warning: Permanent Deletion</span>
            </div>
            <p className="text-red-300/80 text-sm mt-2">
              This will permanently delete {selectedTickets.length} ticket{selectedTickets.length > 1 ? 's' : ''} and all associated data.
              Consider resolving or closing tickets instead of deleting them to maintain support history.
            </p>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsBulkDeleteDialogOpen(false)}
              className="border-vista-light/20 text-vista-light"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleBulkDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete {selectedTickets.length} Ticket{selectedTickets.length > 1 ? 's' : ''}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
