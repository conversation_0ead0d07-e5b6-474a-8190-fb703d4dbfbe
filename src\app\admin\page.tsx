'use client';

import { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import {
  Users,
  Film,
  Tv,
  Activity,
  Clock,
  TrendingUp,
  Server,
  AlertCircle,
  RefreshCw,
  Pie<PERSON>hart,
  BarChart,
  AlertTriangle,
  Loader2,
  ArrowRight,
  Settings,
  Layers,
  Eye,
  UserPlus
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { useDashboardStats } from '@/hooks/useAdminData';
import { AutoRefreshControl } from '@/components/admin/AutoRefreshControl';
import {
  ActivitySkeleton,
  PopularContentSkeleton,
  UserStatsSkeleton,
  ContentStatsSkeleton,
  SystemStatsSkeleton
} from '@/components/admin/DashboardSkeletons';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import { Label } from '@/components/ui/label';
import { UserActivityChart } from '@/components/admin/UserActivityChart';
import { PopularContentTable } from '@/components/admin/PopularContentTable';
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import UserActivityLogs from '@/components/admin/UserActivityLogs';
import SystemLogs from '@/components/admin/SystemLogs';

// Define content item type for proper typing
interface ContentItem {
  id: string;
  title: string;
  type: 'movie' | 'show' | 'tv';
  views: number;
  rating?: number;
  watchTime?: number;
  posterPath?: string;
  avgProgress?: number;
  completionRate?: number;
  releaseDate?: string;
}

// Define activity data type that matches UserActivityChart expectations
interface ActivityData {
  type?: 'user_registration' | 'content_added' | 'system_alert' | 'user_login' | string;
  message?: string;
  details?: string;
  timestamp?: string;
  date?: string;
  logins?: number;
  signups?: number;
  viewTime?: number;
}

// Use memo to optimize the "last updated" display and avoid unnecessary re-renders
function LastUpdatedTime({ timestamp }: { timestamp?: string }) {
  if (!timestamp) {
    return "Just now";
  }

  try {
    return new Date(timestamp).toLocaleTimeString();
  } catch (e) {
    return "Unknown";
  }
}

export default function AdminDashboard() {
  const { user, isAdmin } = useAuth();
  const { data: stats, isLoading: statsLoading, error: statsError, toggleAutoRefresh, isAutoRefreshEnabled, refreshInterval, refetch } = useDashboardStats();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<string>('overview');

  // Create default stats object for safety
  const safeStats = stats || {
    users: { total: 0, active: 0, new: 0 },
    visitors: { total: 0, new: 0 },
    content: {
      movies: 0,
      shows: 0,
      episodes: 0,
      newMovies: 0,
      newShows: 0,
      total: 0
    },
    views: {
      total: 0,
      weekly: 0,
      dailyAverage: 0
    },
    system: {
      status: 'healthy' as const,
      uptime: '0d 0h 0m',
      load: '0%',
      memory: {
        total: 0,
        free: 0,
        used: 0,
        usedPercentage: 0
      },
      platform: '',
      cpus: 0
    },
    activity: [],
    popular: [] as ContentItem[],
    timestamp: new Date().toISOString()
  };

  // Process activity data for the chart - MUST BE BEFORE ANY CONDITIONAL RETURNS
  const activityData = useMemo(() => {
    // Get activity data safely inside the useMemo callback
    const activities = safeStats.activity || [];

    if (!activities.length) {
      console.log('No activity data available in safeStats');
      return [];
    }

    console.log(`Processing ${activities.length} activities for chart`);

    // Count login and signup events for logging
    let loginCount = 0;
    let signupCount = 0;

    // Process the activity data to extract login and signup events
    return activities.map(item => {
      // More comprehensive check for login and signup events
      // Use string comparison to avoid type issues
      const typeStr = String(item.type || '');
      const actionStr = String(item.action || '');
      const messageStr = String(item.message || '');
      const detailsStr = String(item.details || '');

      // Check for login events - more comprehensive matching
      const isLogin =
        typeStr === 'user_login' ||
        (typeStr === 'auth' && (actionStr === 'login' || actionStr === 'signin')) ||
        (actionStr === 'login' || actionStr === 'signin') ||
        messageStr.toLowerCase().includes('logged in') ||
        messageStr.toLowerCase().includes('login') ||
        messageStr.toLowerCase().includes('sign in') ||
        detailsStr.toLowerCase().includes('logged in') ||
        detailsStr.toLowerCase().includes('login') ||
        detailsStr.toLowerCase().includes('sign in');

      // Check for signup events - more comprehensive matching
      const isSignup =
        typeStr === 'user_registration' ||
        (typeStr === 'auth' && (actionStr === 'signup' || actionStr === 'register')) ||
        (actionStr === 'signup' || actionStr === 'register') ||
        messageStr.toLowerCase().includes('registered') ||
        messageStr.toLowerCase().includes('new user') ||
        messageStr.toLowerCase().includes('signup') ||
        messageStr.toLowerCase().includes('sign up') ||
        detailsStr.toLowerCase().includes('registered') ||
        detailsStr.toLowerCase().includes('new user') ||
        detailsStr.toLowerCase().includes('signup') ||
        detailsStr.toLowerCase().includes('sign up');

      // Count events for logging
      if (isLogin) loginCount++;
      if (isSignup) signupCount++;

      // Determine the most appropriate type
      let activityType = 'system_alert';
      if (isSignup) {
        activityType = 'user_registration';
      } else if (isLogin) {
        activityType = 'user_login';
      }

      return {
        type: activityType,
        message: item.message,
        details: item.details,
        timestamp: item.timestamp,
        date: item.timestamp,
        action: item.action,
        // Add explicit login and signup counts
        logins: isLogin ? 1 : 0,
        signups: isSignup ? 1 : 0
      };
    });

    console.log(`Found ${loginCount} login events and ${signupCount} signup events`);

  }, [safeStats.activity]);

  // Handle refresh interval change
  const handleIntervalChange = (interval: number) => {
    toggleAutoRefresh(true, interval);
  };

  // Disable auto-refresh by default - only refresh manually or when component mounts
  useEffect(() => {
    // Force an immediate refresh when the component mounts
    refetch();
    
    // Disable auto-refresh by default
    toggleAutoRefresh(false);

    // Clean up on unmount
    return () => {
      console.log('Cleaning up dashboard auto-refresh');
      toggleAutoRefresh(false);
    };
    // Run this effect only once on mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Parse load percentage from string to number
  const parseLoadPercentage = (loadStr: string): number => {
    return parseInt(loadStr.replace('%', ''), 10) || 0;
  };

  // Get memory usage percentage safely
  const getMemoryPercentage = (): number => {
    return safeStats.system.memory?.usedPercentage || 0;
  };

  // Determine system status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-400';
      case 'warning':
        return 'text-yellow-400';
      case 'critical':
      case 'degraded':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  // Determine progress bar color based on usage
  const getProgressColor = (percentage: number): string => {
    if (percentage > 90) return "hsl(var(--destructive))"; // e.g., Red
    if (percentage > 70) return "hsl(var(--warning))";    // e.g., Yellow/Amber
    return "hsl(var(--primary))"; // Default healthy color (e.g., Blue/Primary)
  };

  // Format bytes
  const formatBytes = (bytes: number, decimals = 2): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }

  // Navigation handlers for section shortcuts
  const handleContentClick = (id: string, type: string) => {
    router.push(`/admin/content/${id}`);
  };

  const navigateTo = (path: string) => {
    router.push(path);
  };

  // Memoize the header to prevent unnecessary re-renders
  const dashboardHeader = (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 className="text-3xl font-bold text-vista-light">Admin Dashboard</h1>
        <p className="text-vista-light/70">
          Monitor system performance and user activities
        </p>
      </div>
      <AutoRefreshControl
        enabled={isAutoRefreshEnabled}
        toggleAutoRefresh={toggleAutoRefresh}
        refreshInterval={refreshInterval || 30000}
        setRefreshInterval={handleIntervalChange}
      />
    </div>
  );

  // If there's an error loading stats, show an error message
  if (statsError) {
    return (
      <div className="h-full flex flex-col items-center justify-center p-6">
        <Card className="w-full max-w-2xl border-red-500/30 bg-red-900/20">
          <CardHeader>
            <CardTitle className="text-2xl font-semibold text-red-400 flex items-center gap-2">
              <AlertTriangle className="h-6 w-6" /> Error Loading Dashboard
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-white/80 mb-6">{statsError.message || "Failed to load dashboard statistics"}</p>
            <div className="flex space-x-4">
              <Button
                onClick={() => refetch()}
                variant="destructive"
                size="sm"
              >
                <RefreshCw className="mr-2 h-4 w-4" /> Retry
              </Button>
              <Button
                onClick={() => router.push('/')}
                variant="outline"
                size="sm"
                className="bg-gray-700 hover:bg-gray-600 border-gray-600"
              >
                Return to Home
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Ready to render dashboard

  return (
    <div className="container mx-auto px-4 py-6 space-y-8 text-vista-light">
      {dashboardHeader}

      {/* Main Dashboard Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Total Users */}
        <Card className="bg-vista-dark/50 border-vista-light/10">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-vista-light/70">Total Users</CardTitle>
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <Skeleton className="h-8 w-20 bg-vista-blue/10" />
            ) : (
              <div>
                <div className="text-3xl font-bold text-vista-light">{safeStats.users.total}</div>
                <p className="text-xs text-vista-light/70 mt-1">
                  +{safeStats.users.new} new this week
                </p>
              </div>
            )}
          </CardContent>
          <CardFooter className="pt-0 pb-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigateTo('/admin/users')}
              className="text-vista-blue hover:text-vista-blue/90 hover:bg-vista-blue/10 -ml-2 text-xs"
            >
              Manage Users <ArrowRight className="ml-1 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>

        {/* Anonymous Visitors */}
        <Card className="bg-vista-dark/50 border-vista-light/10">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-vista-light/70">Anonymous Visitors</CardTitle>
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <Skeleton className="h-8 w-20 bg-vista-blue/10" />
            ) : (
              <div>
                <div className="text-3xl font-bold text-vista-light">{safeStats.visitors.total}</div>
                <p className="text-xs text-vista-light/70 mt-1">
                  +{safeStats.visitors.new} new this week
                </p>
              </div>
            )}
          </CardContent>
          <CardFooter className="pt-0 pb-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigateTo('/admin/visitors')}
              className="text-vista-blue hover:text-vista-blue/90 hover:bg-vista-blue/10 -ml-2 text-xs"
            >
              View Visitors <ArrowRight className="ml-1 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>

        {/* Movies */}
        <Card className="bg-vista-dark/50 border-vista-light/10">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-vista-light/70">Movies</CardTitle>
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <Skeleton className="h-8 w-20 bg-vista-blue/10" />
            ) : (
              <div>
                <div className="text-3xl font-bold text-vista-light">{safeStats.content.movies}</div>
                <p className="text-xs text-vista-light/70 mt-1">
                  +{safeStats.content.newMovies} added this week
                </p>
              </div>
            )}
          </CardContent>
          <CardFooter className="pt-0 pb-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigateTo('/admin/content?type=movie')}
              className="text-vista-blue hover:text-vista-blue/90 hover:bg-vista-blue/10 -ml-2 text-xs"
            >
              Manage Movies <ArrowRight className="ml-1 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>

        {/* TV Shows */}
        <Card className="bg-vista-dark/50 border-vista-light/10">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-vista-light/70">TV Shows</CardTitle>
          </CardHeader>
          <CardContent>
            {statsLoading ? (
              <Skeleton className="h-8 w-20 bg-vista-blue/10" />
            ) : (
              <div>
                <div className="text-3xl font-bold text-vista-light">{safeStats.content.shows}</div>
                <p className="text-xs text-vista-light/70 mt-1">
                  +{safeStats.content.newShows} added this week
                </p>
              </div>
            )}
          </CardContent>
          <CardFooter className="pt-0 pb-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigateTo('/admin/content?type=show')}
              className="text-vista-blue hover:text-vista-blue/90 hover:bg-vista-blue/10 -ml-2 text-xs"
            >
              Manage Shows <ArrowRight className="ml-1 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* User Activity Chart */}
      <Card className="bg-vista-dark/50 border-vista-light/10 overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>User Activity Overview</CardTitle>
            <CardDescription>Signups and logins over the past 7 days</CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              console.log('Manually refreshing dashboard data');
              refetch();
            }}
            className="transition-all duration-200 hover:bg-vista-blue/10 hover:text-vista-blue hover:border-vista-blue/30 active:scale-95"
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${statsLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </CardHeader>
        <UserActivityChart data={activityData} />
        <CardFooter className="bg-card/30 border-t border-t-muted/20 p-3">
          <Button variant="ghost" size="sm" onClick={() => navigateTo('/admin/analytics')}>
            <BarChart className="h-4 w-4 mr-2" />
            View Detailed Analytics
          </Button>
        </CardFooter>
      </Card>

      {/* System Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
        {/* System Health Card */}
        <Card className="bg-vista-dark/50 border-vista-light/10">
          <CardHeader>
            <CardTitle>System Health</CardTitle>
            <CardDescription>Current system status and performance</CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="p-6 pt-2 space-y-6">
              {/* System Status */}
              <div className={cn(
                "p-4 rounded-lg",
                safeStats.system.status === 'warning' && "bg-yellow-900/20 border border-yellow-500/30",
                safeStats.system.status === 'critical' && "bg-red-900/20 border border-red-500/30",
                safeStats.system.status === 'degraded' && "bg-orange-900/20 border border-orange-500/30",
                safeStats.system.status === 'healthy' && "bg-green-900/20 border border-green-500/30"
              )}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {safeStats.system.status === 'healthy' ? (
                      <span className="text-green-400"><AlertCircle className="h-5 w-5" /></span>
                    ) : safeStats.system.status === 'warning' ? (
                      <span className="text-yellow-400"><AlertTriangle className="h-5 w-5" /></span>
                    ) : (
                      <span className="text-red-400"><AlertCircle className="h-5 w-5" /></span>
                    )}
                    <span className={cn("font-medium", getStatusColor(safeStats.system.status))}>
                      System Status: {safeStats.system.status.charAt(0).toUpperCase() + safeStats.system.status.slice(1)}
                    </span>
                  </div>
                  <span className="text-sm text-vista-light/70">
                    Uptime: {safeStats.system.uptime}
                  </span>
                </div>
              </div>

              {/* Resource Usage */}
              <div className="space-y-4">
                {/* Memory Usage */}
                <div>
                  <div className="flex justify-between mb-1">
                    <Label className="text-vista-light/70">Memory Usage</Label>
                    <span className="text-sm text-vista-light/70">
                      {getMemoryPercentage()}%
                    </span>
                  </div>
                  <Progress
                    value={getMemoryPercentage()}
                    className="h-2 bg-vista-light/10"
                  />
                  <div className="flex justify-between text-xs text-vista-light/50 mt-1">
                    <span>
                      {formatBytes(safeStats.system.memory?.used || 0)} used
                    </span>
                    <span>
                      {formatBytes(safeStats.system.memory?.total || 0)} total
                    </span>
                  </div>
                </div>

                {/* CPU Usage */}
                <div>
                  <div className="flex justify-between mb-1">
                    <Label className="text-vista-light/70">CPU Load</Label>
                    <span className="text-sm text-vista-light/70">
                      {safeStats.system.load}
                    </span>
                  </div>
                  <Progress
                    value={parseLoadPercentage(safeStats.system.load)}
                    className="h-2 bg-vista-light/10"
                  />
                  <div className="flex justify-between text-xs text-vista-light/50 mt-1">
                    <span>
                      {safeStats.system.cpus} CPUs
                    </span>
                    <span>
                      {safeStats.system.platform}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="bg-card/30 border-t border-t-muted/20 p-3">
            <Button variant="ghost" size="sm" onClick={() => navigateTo('/admin/system')}>
              <Server className="h-4 w-4 mr-2" />
              View System Details
            </Button>
          </CardFooter>
        </Card>

        {/* Recent Activity Card */}
        <Card className="bg-vista-dark/50 border-vista-light/10">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest user actions in the system</CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="p-4 max-h-[400px] overflow-auto">
              {statsLoading ? (
                <ActivitySkeleton />
              ) : (
                <div className="space-y-3">
                  {safeStats.activity && safeStats.activity.length > 0 ? (
                    safeStats.activity.slice(0, 8).map((activity, index) => (
                      <div key={index} className="flex items-start gap-3 p-2 rounded-md bg-vista-dark/30 border border-vista-light/5">
                        <div className={cn(
                          "mt-0.5 p-1.5 rounded-full",
                          (() => {
                            // Determine activity type based on properties
                            const activityType = (() => {
                              const typeStr = String(activity.type || '');
                              const actionStr = String(activity.action || '');
                              const messageStr = String(activity.message || '');

                              // Check for signup/registration
                              if (typeStr === 'user_registration') return 'signup';
                              if (typeStr === 'auth' && actionStr === 'signup') return 'signup';
                              if (actionStr === 'signup' || actionStr === 'register') return 'signup';
                              if (messageStr.toLowerCase().includes('registered') ||
                                  messageStr.toLowerCase().includes('new user')) return 'signup';

                              // Check for login
                              if (typeStr === 'user_login') return 'login';
                              if (typeStr === 'auth' &&
                                  (actionStr === 'login' || actionStr === 'signin')) return 'login';
                              if (actionStr === 'login' || actionStr === 'signin') return 'login';
                              if (messageStr.toLowerCase().includes('logged in')) return 'login';

                              // Default to system
                              if (typeStr === 'system_alert' || typeStr === 'system') return 'system';

                              return 'system';
                            })();

                            // Return appropriate class based on activity type
                            if (activityType === 'signup') return "bg-green-900/30 text-green-400";
                            if (activityType === 'login') return "bg-blue-900/30 text-blue-400";
                            return "bg-amber-900/30 text-amber-400";
                          })()
                        )}>
                          {(() => {
                            // Determine activity type based on properties
                            const activityType = (() => {
                              const typeStr = String(activity.type || '');
                              const actionStr = String(activity.action || '');
                              const messageStr = String(activity.message || '');

                              // Check for signup/registration
                              if (typeStr === 'user_registration') return 'signup';
                              if (typeStr === 'auth' && actionStr === 'signup') return 'signup';
                              if (actionStr === 'signup' || actionStr === 'register') return 'signup';
                              if (messageStr.toLowerCase().includes('registered') ||
                                  messageStr.toLowerCase().includes('new user')) return 'signup';

                              // Check for login
                              if (typeStr === 'user_login') return 'login';
                              if (typeStr === 'auth' &&
                                  (actionStr === 'login' || actionStr === 'signin')) return 'login';
                              if (actionStr === 'login' || actionStr === 'signin') return 'login';
                              if (messageStr.toLowerCase().includes('logged in')) return 'login';

                              // Default to system
                              return 'system';
                            })();

                            // Return appropriate icon based on activity type
                            if (activityType === 'signup') return <UserPlus className="h-3.5 w-3.5" />;
                            if (activityType === 'login') return <Users className="h-3.5 w-3.5" />;
                            return <AlertCircle className="h-3.5 w-3.5" />;
                          })()}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-vista-light truncate">
                            {activity.message || 'System event'}
                          </p>
                          <p className="text-xs text-vista-light/60 truncate">
                            {activity.details || 'No details available'}
                          </p>
                        </div>
                        <div className="text-xs text-vista-light/50 whitespace-nowrap">
                          {activity.timestamp ? new Date(activity.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : 'N/A'}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-4 text-center text-vista-light/50">
                      <AlertCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>No recent activity data available</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter className="bg-card/30 border-t border-t-muted/20 p-3">
            <Button variant="ghost" size="sm" onClick={() => navigateTo('/admin/activity')}>
              <Activity className="h-4 w-4 mr-2" />
              View All Activity
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* System Logs */}
      <Card className="bg-vista-dark/50 border-vista-light/10 mt-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>System Logs</CardTitle>
              <CardDescription>Recent system events and alerts</CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={() => navigateTo('/admin/system/logs')}>
              View All Logs
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-[300px] px-4 custom-scrollbar" type="always">
            <SystemLogs limit={10} height="300px" autoRefresh={false} />
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}
