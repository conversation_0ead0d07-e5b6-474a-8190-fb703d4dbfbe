import { NextRequest, NextResponse } from 'next/server';

/**
 * POST /api/activity/log
 * Log user activity
 */
export async function POST(request: NextRequest) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get data from request
    const data = await request.json();
    const { type, action, details, metadata } = data;

    // Validate required fields
    if (!type || !action || !details) {
      return NextResponse.json(
        { error: 'Type, action, and details are required' },
        { status: 400 }
      );
    }

    // Get IP and user agent
    const ip = request.headers.get('x-forwarded-for') ||
               request.headers.get('x-real-ip') ||
               'unknown';

    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Get the UserActivity model directly
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', new mongoose.default.Schema({
                          userId: mongoose.default.Schema.Types.ObjectId,
                          type: String,
                          action: String,
                          details: String,
                          metadata: mongoose.default.Schema.Types.Mixed,
                          timestamp: Date,
                          ipAddress: String,
                          userAgent: String
                        }));

    // Create the activity log
    const activity = await UserActivity.create({
      userId: new mongoose.default.Types.ObjectId(userId),
      type,
      action,
      details,
      metadata: metadata || {},
      timestamp: new Date(),
      ipAddress: ip,
      userAgent
    });

    return NextResponse.json({
      success: true,
      activityId: activity._id.toString()
    });
  } catch (error) {
    console.error('Error logging user activity:', error);
    return NextResponse.json(
      { error: 'Failed to log user activity', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
