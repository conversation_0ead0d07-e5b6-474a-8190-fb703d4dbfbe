import { NextRequest, NextResponse } from 'next/server';
import { getAdminUserId } from '@/lib/admin-auth';
import { ensureMongooseConnection } from '@/lib/mongodb';
import User from '@/models/User';
import mongoose from 'mongoose';

/**
 * PUT /api/admin/users/bulk
 * Perform bulk actions on users
 */
export async function PUT(request: NextRequest) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      name: String,
      email: String,
      profileImage: String,
      role: String,
      status: String,
      emailVerified: Date,
      lastLogin: Date
    }, {
      timestamps: true
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as { role?: string }).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Get request data
    const data = await request.json();
    const { userIds, action } = data;

    // Validate request
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json({ error: 'No users specified' }, { status: 400 });
    }

    if (!action) {
      return NextResponse.json({ error: 'No action specified' }, { status: 400 });
    }

    // Validate user IDs
    const validUserIds = userIds.filter(id => mongoose.default.Types.ObjectId.isValid(id));
    if (validUserIds.length === 0) {
      return NextResponse.json({ error: 'No valid user IDs provided' }, { status: 400 });
    }

    let result;

    // Perform the requested action
    switch (action) {
      case 'changeRole':
        const { role } = data;
        if (!role || !['user', 'moderator', 'admin'].includes(role)) {
          return NextResponse.json({ error: 'Invalid role specified' }, { status: 400 });
        }

        result = await User.updateMany(
          { _id: { $in: validUserIds } },
          { $set: { role } }
        );

        // Log admin activity directly
        try {
          await UserActivity.create({
            userId: new mongoose.default.Types.ObjectId(userId),
            type: 'admin',
            action: 'bulk_change_role',
            details: `Admin changed role to ${role} for ${result.modifiedCount} users`,
            ipAddress: request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
            userAgent: request.headers.get('user-agent') || 'unknown',
            timestamp: new Date(),
            metadata: {
              userIds: validUserIds,
              role,
              affectedCount: result.modifiedCount
            }
          });
        } catch (logError) {
          console.error('Error logging admin activity:', logError);
          // Continue even if logging fails
        }

        return NextResponse.json({
          message: `Updated ${result.modifiedCount} users to role: ${role}`,
          modifiedCount: result.modifiedCount
        });

      case 'changeVerification':
        const { verified } = data;
        if (verified === undefined) {
          return NextResponse.json({ error: 'Verification status not specified' }, { status: 400 });
        }

        result = await User.updateMany(
          { _id: { $in: validUserIds } },
          { $set: { emailVerified: verified ? new Date() : null } }
        );

        // Log admin activity directly
        try {
          await UserActivity.create({
            userId: new mongoose.default.Types.ObjectId(userId),
            type: 'admin',
            action: 'bulk_change_verification',
            details: `Admin ${verified ? 'verified' : 'unverified'} ${result.modifiedCount} users`,
            ipAddress: request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
            userAgent: request.headers.get('user-agent') || 'unknown',
            timestamp: new Date(),
            metadata: {
              userIds: validUserIds,
              verified,
              affectedCount: result.modifiedCount
            }
          });
        } catch (logError) {
          console.error('Error logging admin activity:', logError);
          // Continue even if logging fails
        }

        return NextResponse.json({
          message: `Updated verification status for ${result.modifiedCount} users`,
          modifiedCount: result.modifiedCount
        });

      default:
        return NextResponse.json({ error: 'Invalid action specified' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error performing bulk user action:', error);
    return NextResponse.json(
      { error: 'Failed to perform bulk action on users', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/users/bulk
 * Delete multiple users
 */
export async function DELETE(request: NextRequest) {
  try {
    // Get the user ID from the cookie directly
    const userId = request.cookies.get('userId')?.value;
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Get the User model directly
    const UserSchema = new mongoose.default.Schema({
      name: String,
      email: String,
      profileImage: String,
      role: String,
      status: String,
      emailVerified: Date,
      lastLogin: Date
    }, {
      timestamps: true
    });

    const User = mongoose.default.models.User ||
                mongoose.default.model('User', UserSchema);

    // Check if user is admin
    const user = await User.findById(userId).select('role').lean();
    if (!user || (user as { role?: string }).role !== 'admin') {
      return NextResponse.json({ error: "Forbidden: You do not have permission to access this resource." }, { status: 403 });
    }

    // Define the UserActivity schema directly
    const UserActivitySchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      type: String,
      action: String,
      details: String,
      ipAddress: String,
      userAgent: String,
      timestamp: { type: Date, default: Date.now },
      metadata: mongoose.default.Schema.Types.Mixed
    }, {
      timestamps: true
    });

    // Get the UserActivity model
    const UserActivity = mongoose.default.models.UserActivity ||
                        mongoose.default.model('UserActivity', UserActivitySchema);

    // Get request data
    const data = await request.json();
    const { userIds } = data;

    // Validate request
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json({ error: 'No users specified' }, { status: 400 });
    }

    // Validate user IDs
    const validUserIds = userIds.filter(id => mongoose.default.Types.ObjectId.isValid(id));
    if (validUserIds.length === 0) {
      return NextResponse.json({ error: 'No valid user IDs provided' }, { status: 400 });
    }

    // Delete users
    const result = await User.deleteMany({ _id: { $in: validUserIds } });

    // Log admin activity directly
    try {
      await UserActivity.create({
        userId: new mongoose.default.Types.ObjectId(userId),
        type: 'admin',
        action: 'bulk_delete_users',
        details: `Admin deleted ${result.deletedCount} users`,
        ipAddress: request.headers.get('x-forwarded-for')?.split(',')[0] || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        timestamp: new Date(),
        metadata: {
          userIds: validUserIds,
          affectedCount: result.deletedCount
        }
      });
    } catch (logError) {
      console.error('Error logging admin activity:', logError);
      // Continue even if logging fails
    }

    return NextResponse.json({
      message: `Deleted ${result.deletedCount} users`,
      deletedCount: result.deletedCount
    });
  } catch (error) {
    console.error('Error deleting users:', error);
    return NextResponse.json(
      { error: 'Failed to delete users', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
