import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import BannerAd from '@/models/BannerAd';
import mongoose from 'mongoose';

/**
 * POST /api/banner-ads/analytics
 * Public endpoint to track banner ad analytics (views, clicks)
 * This endpoint is public and doesn't require authentication
 */
export async function POST(request: NextRequest) {
  try {
    await ensureMongooseConnection();

    const body = await request.json();
    const { bannerId, action } = body;

    // Validate banner ID
    if (!bannerId || !mongoose.Types.ObjectId.isValid(bannerId)) {
      return NextResponse.json(
        { error: 'Invalid banner ad ID' },
        { status: 400 }
      );
    }

    // Validate action type
    if (!['view', 'click'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action type. Must be view or click' },
        { status: 400 }
      );
    }

    // Check if banner ad exists and is active
    const bannerAd = await BannerAd.findById(bannerId);
    if (!bannerAd) {
      return NextResponse.json(
        { error: 'Banner ad not found' },
        { status: 404 }
      );
    }

    // Only track analytics for active banners
    if (!bannerAd.isActive) {
      return NextResponse.json(
        { success: true, message: 'Banner is not active' },
        { status: 200 }
      );
    }

    // Update analytics based on action
    const updateField = action === 'view' ? 'analytics.views' : 'analytics.clicks';

    await BannerAd.findByIdAndUpdate(
      bannerId,
      { $inc: { [updateField]: 1 } },
      { new: true }
    );

    return NextResponse.json({
      success: true,
      message: `${action} tracked successfully`
    });

  } catch (error) {
    console.error('Error tracking banner analytics:', error);
    return NextResponse.json(
      { error: 'Failed to track analytics' },
      { status: 500 }
    );
  }
} 