import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongodb';
import { HelpTicket, HelpCategory, HelpTicketResponse } from '@/models/HelpTicket';
import User from '@/models/User';
import { authMiddleware } from '@/lib/middleware';
import { ensureApplicationInitialized } from '@/lib/initialization';

interface TicketQuery {
  userId?: string;
  status?: string;
  category?: string;
  priority?: string;
  assignedTo?: string | { $exists: boolean };
  escalated?: boolean | { $ne: boolean };
  createdAt?: {
    $gte?: Date;
    $lte?: Date;
  };
  $text?: { $search: string };
}

/**
 * GET /api/help/tickets
 * Get help tickets for the current user or all tickets for admins
 */
export async function GET(request: NextRequest) {
  try {
    await ensureMongooseConnection();

    // Get userId from query parameters (like other working API routes)
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized - userId required' }, { status: 401 });
    }

    // Get user to check role
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get query parameters for filtering and pagination
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const category = searchParams.get('category');
    const priority = searchParams.get('priority');
    const search = searchParams.get('search');
    const assignedTo = searchParams.get('assignedTo');
    const assigned = searchParams.get('assigned');
    const escalated = searchParams.get('escalated');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');

    // Build query
    const query: TicketQuery = {};
    
    // If not admin, only show user's own tickets
    if (user.role !== 'admin' && user.role !== 'superadmin') {
      query.userId = userId;
    }

    if (status) query.status = status;
    if (category) query.category = category;
    if (priority) query.priority = priority;
    if (assignedTo) query.assignedTo = assignedTo;

    // Handle assignment filter
    if (assigned === 'unassigned') {
      query.assignedTo = { $exists: false };
    } else if (assigned === 'assigned') {
      query.assignedTo = { $exists: true };
    } else if (assigned && assigned !== 'all') {
      query.assignedTo = assigned;
    }

    // Handle escalation filter
    if (escalated === 'escalated') {
      query.escalated = true;
    } else if (escalated === 'not_escalated') {
      query.escalated = { $ne: true };
    }

    // Handle date range filter
    if (dateFrom || dateTo) {
      query.createdAt = {};
      if (dateFrom) {
        query.createdAt.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        const endDate = new Date(dateTo);
        endDate.setHours(23, 59, 59, 999); // End of day
        query.createdAt.$lte = endDate;
      }
    }

    // Add text search if provided
    if (search) {
      query.$text = { $search: search };
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get tickets with populated fields
    const tickets = await HelpTicket.find(query)
      .populate('userId', 'name email profileImage')
      .populate('assignedTo', 'name email')
      .populate('lastResponseBy', 'name email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const total = await HelpTicket.countDocuments(query);

    return NextResponse.json({
      tickets,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching help tickets:', error);
    return NextResponse.json(
      { error: 'Failed to fetch help tickets' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/help/tickets
 * Create a new help ticket
 */
export async function POST(request: NextRequest) {
  try {
    await ensureMongooseConnection();

    // Get request body and extract data
    const body = await request.json();
    const {
      userId,
      category,
      priority = 'medium',
      subject,
      description,
      attachments = [],
      tags = [],
      metadata = {}
    } = body;

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized - userId required' }, { status: 401 });
    }

    // Get user details
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Validate required fields
    if (!category || !subject || !description) {
      return NextResponse.json(
        { error: 'Category, subject, and description are required' },
        { status: 400 }
      );
    }

    // Validate category and ensure categories are seeded
    const allowedCategories = ['subscription', 'technical', 'billing', 'content', 'account', 'bug_report', 'feature_request', 'other'];
    if (!allowedCategories.includes(category)) {
      return NextResponse.json(
        { error: 'Invalid category' },
        { status: 400 }
      );
    }

    // Ensure application is properly initialized
    try {
      await ensureApplicationInitialized();
    } catch (initError) {
      console.warn('Warning: Could not ensure application initialization:', initError);
      // Continue with ticket creation even if initialization fails
    }

    // Find the category in the database for ticket count tracking
    const categoryDoc = await HelpCategory.findOne({ slug: category, isActive: true });

    // Generate ticket number manually as backup
    const generateTicketNumber = () => {
      const timestamp = Date.now().toString(36).toUpperCase();
      const random = Math.random().toString(36).substring(2, 6).toUpperCase();
      return `SV-${timestamp}-${random}`;
    };

    // Create the ticket
    const ticketNumber = generateTicketNumber();

    const ticket = new HelpTicket({
      ticketNumber, // Explicitly set the ticket number
      userId,
      userEmail: user.email,
      userName: user.name,
      category,
      priority,
      subject,
      description,
      attachments,
      tags,
      metadata: {
        ...metadata,
        subscriptionStatus: user.subscriptionStatus,
        lastLoginDate: user.lastLogin
      }
    });

    await ticket.save();

    // Update category ticket count if category exists in database
    if (categoryDoc) {
      await HelpCategory.findByIdAndUpdate(
        categoryDoc._id,
        { $inc: { ticketCount: 1 } }
      );
    }

    // Populate the ticket before returning
    const populatedTicket = await HelpTicket.findById(ticket._id)
      .populate('userId', 'name email profileImage')
      .lean();

    return NextResponse.json({
      message: 'Help ticket created successfully',
      ticket: populatedTicket
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating help ticket:', error);
    return NextResponse.json(
      { error: 'Failed to create help ticket' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/help/tickets
 * Bulk update tickets (admin only)
 */
export async function PUT(request: NextRequest) {
  try {
    // Use the same authentication pattern as other API routes
    const authResult = await authMiddleware(request);
    if (!authResult.isAuthenticated || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = authResult.user;

    // Check if user is admin
    if (user.role !== 'admin' && user.role !== 'superadmin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { ticketIds, action, updates, ...actionData } = body;

    if (!ticketIds || !Array.isArray(ticketIds) || ticketIds.length === 0) {
      return NextResponse.json(
        { error: 'Ticket IDs are required' },
        { status: 400 }
      );
    }

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    let resultCount = 0;
    let message = '';

    switch (action) {
      case 'delete':
        // Delete tickets and their responses
        await HelpTicketResponse.deleteMany({ ticketId: { $in: ticketIds } });
        const deleteResult = await HelpTicket.deleteMany({ _id: { $in: ticketIds } });
        resultCount = deleteResult.deletedCount;
        message = `Deleted ${resultCount} tickets`;
        break;

      case 'resolve':
        const resolveResult = await HelpTicket.updateMany(
          { _id: { $in: ticketIds } },
          {
            status: 'resolved',
            resolvedAt: new Date(),
            resolvedBy: user._id,
            updatedAt: new Date(),
            ...actionData
          }
        );
        resultCount = resolveResult.modifiedCount;
        message = `Resolved ${resultCount} tickets`;
        break;

      case 'close':
        const closeResult = await HelpTicket.updateMany(
          { _id: { $in: ticketIds } },
          {
            status: 'closed',
            updatedAt: new Date(),
            ...actionData
          }
        );
        resultCount = closeResult.modifiedCount;
        message = `Closed ${resultCount} tickets`;
        break;

      case 'assign':
        if (!actionData.assignedTo) {
          return NextResponse.json(
            { error: 'Assigned user ID is required for assignment' },
            { status: 400 }
          );
        }
        const assignResult = await HelpTicket.updateMany(
          { _id: { $in: ticketIds } },
          {
            assignedTo: actionData.assignedTo,
            assignedToName: actionData.assignedToName,
            updatedAt: new Date()
          }
        );
        resultCount = assignResult.modifiedCount;
        message = `Assigned ${resultCount} tickets`;
        break;

      default:
        // Fallback to generic update for backward compatibility
        const updateResult = await HelpTicket.updateMany(
          { _id: { $in: ticketIds } },
          {
            ...updates,
            ...actionData,
            updatedAt: new Date()
          }
        );
        resultCount = updateResult.modifiedCount;
        message = `Updated ${resultCount} tickets`;
    }

    return NextResponse.json({
      message,
      modifiedCount: resultCount
    });

  } catch (error) {
    console.error('Error bulk updating tickets:', error);
    return NextResponse.json(
      { error: 'Failed to update tickets' },
      { status: 500 }
    );
  }
}
