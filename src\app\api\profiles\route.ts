import { NextRequest, NextResponse } from 'next/server';
import { ProfileSession } from '@/lib/types';

/**
 * GET /api/profiles
 * Get all profiles for the current user
 */
export async function GET(request: NextRequest) {
  try {
    // Get user ID from the URL or authorization
    const userId = request.nextUrl.searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Define the Profile schema directly
    const ProfileSchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      name: String,
      avatar: String,
      isKids: Boolean,
      isPrimary: Boolean
    }, {
      timestamps: true
    });

    // Get the Profile model
    const Profile = mongoose.default.models.Profile ||
                   mongoose.default.model('Profile', ProfileSchema);

    // Find all profiles for this user
    const profiles = await Profile.find({ userId })
      .sort({ isPrimary: -1, createdAt: 1 })
      .lean();

    // Transform to client-friendly format
    const profilesResponse: ProfileSession[] = profiles.map((profile: any) => ({
      id: profile._id.toString(),
      name: profile.name,
      avatar: profile.avatar,
      isKids: profile.isKids,
      isPrimary: profile.isPrimary
      // Note: Mongoose virtuals or methods are NOT available on leaned documents
    }));

    return NextResponse.json({ profiles: profilesResponse });
  } catch (error) {
    console.error('Error fetching profiles:', error);
    return NextResponse.json(
      { error: 'Failed to fetch profiles', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/profiles
 * Create a new profile for the current user
 */
export async function POST(request: NextRequest) {
  try {
    // Import mongoose dynamically to avoid circular dependencies
    const mongoose = await import('mongoose');

    // Connect to MongoDB directly
    const MONGODB_URI = process.env.MONGODB_URI;
    if (!MONGODB_URI) {
      throw new Error('MONGODB_URI is not defined');
    }

    // Connect if not already connected
    if (mongoose.default.connection.readyState !== 1) {
      await mongoose.default.connect(MONGODB_URI);
    }

    // Define the Profile schema directly
    const ProfileSchema = new mongoose.default.Schema({
      userId: mongoose.default.Schema.Types.ObjectId,
      name: String,
      avatar: String,
      isKids: Boolean,
      isPrimary: Boolean
    }, {
      timestamps: true
    });

    // Get the Profile model
    const Profile = mongoose.default.models.Profile ||
                   mongoose.default.model('Profile', ProfileSchema);

    // Parse the request body
    const data = await request.json();
    const { userId, name, avatar, isKids } = data;

    // Validate required fields
    if (!userId || !name) {
      return NextResponse.json(
        { error: 'User ID and name are required' },
        { status: 400 }
      );
    }

    // Check if this is the first profile for this user (should be primary)
    const existingProfiles = await Profile.countDocuments({ userId });
    const isPrimary = existingProfiles === 0;

    // Create the profile
    const newProfile = new Profile({
      userId, // MongoDB will convert this to ObjectId
      name,
      avatar: avatar || `/avatars/avatar-${Math.floor(Math.random() * 6) + 1}.png`,
      isKids: isKids || false,
      isPrimary,
      // Other fields will use default values from the schema
    });

    await newProfile.save();

    // Return the created profile
    return NextResponse.json({
      profile: {
        id: newProfile._id.toString(),
        name: newProfile.name,
        avatar: newProfile.avatar,
        isKids: newProfile.isKids,
        isPrimary: newProfile.isPrimary
      }
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating profile:', error);
    return NextResponse.json(
      { error: 'Failed to create profile', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}