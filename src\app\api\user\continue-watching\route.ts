import { NextRequest, NextResponse } from 'next/server';
import { ensureMongooseConnection } from '@/lib/mongoose';
import UserContentInteraction from '@/models/UserContentInteraction';
import Content from '@/models/Content';
import mongoose from 'mongoose';
import { continueWatching as mockContinueWatching } from '@/data/content';

/**
 * GET /api/user/continue-watching
 * Fetch the user's continue watching list
 */
export async function GET(request: NextRequest) {
  try {
    // Get the user ID from the query parameters
    const userId = request.nextUrl.searchParams.get('userId');
    
    // If no userId is provided, return mock data (for non-authenticated users)
    if (!userId) {
      return NextResponse.json({
        success: true,
        items: mockContinueWatching,
        isMockData: true
      });
    }
    
    // Connect to MongoDB
    await ensureMongooseConnection();
    
    // Validate MongoDB ObjectId format
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return NextResponse.json(
        { error: 'Invalid user ID format' },
        { status: 400 }
      );
    }
    
    // Find the user's most recent content interactions with progress info
    const watchHistory = await UserContentInteraction.aggregate([
      { 
        $match: { 
          userId: new mongoose.Types.ObjectId(userId),
          interactionType: 'view',
          // Only include items with progress information
          progress: { $exists: true, $gt: 0 }
        } 
      },
      {
        // Group by content to get the most recent interaction for each content
        $sort: { timestamp: -1 }
      },
      {
        $group: {
          _id: '$contentId',
          timestamp: { $first: '$timestamp' },
          progress: { $first: '$progress' },
          duration: { $first: '$duration' },
          metadata: { $first: '$metadata' }
        }
      },
      {
        // Limit to the most recent 10 items
        $limit: 10
      },
      {
        // Join with the content collection to get content details
        $lookup: {
          from: 'contents',
          localField: '_id',
          foreignField: '_id',
          as: 'content'
        }
      },
      {
        $unwind: {
          path: '$content',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        // Format the output
        $project: {
          _id: 0,
          id: { $toString: '$_id' },
          title: '$content.title',
          type: '$content.type',
          image: '$content.posterPath',
          progress: 1,
          timestamp: 1,
          duration: 1,
          episode: { $ifNull: ['$metadata.episode', null] },
          season: { $ifNull: ['$metadata.season', null] }
        }
      }
    ]);
    
    // If no watch history is found, return mock data
    if (!watchHistory || watchHistory.length === 0) {
      return NextResponse.json({
        success: true,
        items: mockContinueWatching,
        isMockData: true,
        message: 'No watch history found, returning mock data'
      });
    }
    
    // Format the watch history items for the continue watching section
    const continueWatchingItems = watchHistory.map(item => ({
      id: item.id,
      title: item.title,
      type: item.type,
      image: item.image || 'https://images.unsplash.com/photo-1536440136628-849c177e76a1?q=80&w=2125', // Fallback image
      progress: item.progress,
      episode: item.episode ? `S${item.season}E${item.episode}` : undefined,
      season: item.season,
      timestamp: item.timestamp,
      duration: item.duration
    }));
    
    // Return the continue watching items
    return NextResponse.json({
      success: true,
      items: continueWatchingItems,
      isMockData: false
    });
    
  } catch (error) {
    console.error('Error fetching continue watching data:', error);
    
    // Return mock data in case of error
    return NextResponse.json({
      success: true,
      items: mockContinueWatching,
      isMockData: true,
      error: 'Error fetching continue watching data'
    });
  }
}
