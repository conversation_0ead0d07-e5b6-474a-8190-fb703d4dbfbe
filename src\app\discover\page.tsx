'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Film, 
  Monitor, 
  Calendar,
  Eye,
  RotateCcw,
  AlertCircle,
  ChevronLeft,
  ChevronRight,
  Search,
  Settings,
  Globe
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { VIDSRC_DOMAINS } from '@/lib/vidsrc-api';
import { VidSrcMovie, VidSrcShow, VidSrcEpisode } from '@/lib/vidsrc-api';
import { useURLPagination } from '@/hooks/useURLPagination';
import { useScrollManager } from '@/hooks/useScrollManager';
import { EnhancedPagination } from '@/components/ui/enhanced-pagination';

export default function DiscoverPage() {
  const [activeTab, setActiveTab] = useState('movies');
  const [movies, setMovies] = useState<VidSrcMovie[]>([]);
  const [shows, setShows] = useState<VidSrcShow[]>([]);
  const [episodes, setEpisodes] = useState<VidSrcEpisode[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDomain, setSelectedDomain] = useState<string | null>(null);
  const [dataSource, setDataSource] = useState<'tmdb' | 'vidsrc'>('tmdb');
  const [totalPages, setTotalPages] = useState(1);
  const router = useRouter();

  // Use URL-based pagination
  const { currentPage: page, setPage, resetPage } = useURLPagination();
  
  // Use scroll manager for better scroll handling
  const { saveScrollPosition } = useScrollManager();

  // Reset page when switching tabs
  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab);
    resetPage();
  };

  useEffect(() => {
    setLoading(true);
    setError(null);
    
    // Load real data
    const loadRealData = async () => {
      try {
        await loadContent(dataSource);
      } catch (error) {
        console.error('Failed to load content:', error);
        setError('Failed to load content. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    loadRealData();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, page, selectedDomain, dataSource]);

  const loadContent = async (source: 'tmdb' | 'vidsrc' = 'tmdb') => {
    try {
      setLoading(true);
      let apiUrl = `/api/vidsrc?type=${activeTab}&page=${page}`;
      
      // Add domain param if one is manually selected
      if (selectedDomain) {
        apiUrl += `&domain=${selectedDomain}`;
      }
      
      // Add data source params
      if (source === 'vidsrc') {
        apiUrl += `&tmdb=false`;
      }
      
      console.log(`Fetching from API: ${apiUrl}`);
      const response = await fetch(apiUrl);
      
      // If response is not OK, handle the error
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to fetch ${activeTab}: ${response.status}`);
      }
      
      const responseData = await response.json();
      console.log(`API response for ${activeTab}:`, responseData);
      
      // Additional error checking for empty or invalid responses
      if (responseData === null || responseData === undefined) {
        throw new Error(`Empty response received for ${activeTab}`);
      }
      
      // Handle both array and object responses with proper typing
      let dataArray: (VidSrcMovie | VidSrcShow | VidSrcEpisode)[] = [];
      
      if (Array.isArray(responseData)) {
        dataArray = responseData;
      } else if (responseData.data && Array.isArray(responseData.data)) {
        dataArray = responseData.data;
      } else if (responseData.results && Array.isArray(responseData.results)) {
        dataArray = responseData.results;
      } else {
        console.warn('Unexpected response format:', responseData);
        throw new Error(`Unexpected response format for ${activeTab}`);
      }

      console.log(`Processed ${dataArray.length} items for ${activeTab}`);

      // Update state based on active tab with proper type casting
      if (activeTab === 'movies') {
        setMovies(dataArray as VidSrcMovie[]);
      } else if (activeTab === 'shows') {
        setShows(dataArray as VidSrcShow[]);
      } else if (activeTab === 'episodes') {
        setEpisodes(dataArray as VidSrcEpisode[]);
      }

      setError(null);
      
    } catch (error) {
      console.error(`Error loading ${activeTab}:`, error);
      const errorMessage = error instanceof Error ? error.message : `Failed to load ${activeTab}`;
      setError(errorMessage);
      
      // Set empty arrays on error instead of mock data
      if (activeTab === 'movies') {
        setMovies([]);
      } else if (activeTab === 'shows') {
        setShows([]);
      } else if (activeTab === 'episodes') {
        setEpisodes([]);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    setError(null);
    loadContent(dataSource);
  };

  const handleDomainChange = (domain: string) => {
    setSelectedDomain(domain === 'auto' ? null : domain);
  };

  const handleContentClick = (item: VidSrcMovie | VidSrcShow | VidSrcEpisode) => {
    if (!item.id) return;
    
    // Save scroll position before navigation
    saveScrollPosition();
    
         // Get content type for routing
     const contentType = activeTab === 'movies' ? 'movie' : 'tv';
    
    // Handle episodes specially
    if (activeTab === 'episodes' && 'season' in item && 'episode' in item) {
      const episode = item as VidSrcEpisode;
      const season = episode.season || '1';
      const episodeNum = episode.episode || '1';
      router.push(`/watch/${item.id}?type=${contentType}&season=${season}&episode=${episodeNum}`);
    } else {
      router.push(`/watch/${item.id}?type=${contentType}`);
    }
  };

  // Get current items for display
  const getCurrentItems = (): (VidSrcMovie | VidSrcShow | VidSrcEpisode)[] => {
    switch (activeTab) {
      case 'movies':
        return movies;
      case 'shows':
        return shows;
      case 'episodes':
        return episodes;
      default:
        return [];
    }
  };

  const currentItems = getCurrentItems();

  return (
    <div className="min-h-screen bg-[#0A0B0F] text-white">
      {/* Header */}
      <div className="px-6 py-8 border-b border-[#1A1A1E]">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold mb-2">Discover Content</h1>
          <p className="text-gray-400">Browse the latest movies, shows, and episodes</p>
        </div>
      </div>

      {/* Controls */}
      <div className="px-6 py-6 border-b border-[#1A1A1E]">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            {/* Content Type Tabs */}
            <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full lg:w-auto">
              <TabsList className="grid w-full lg:w-auto grid-cols-3 bg-[#1A1A1E] border border-[#2A2A2E]">
                <TabsTrigger value="movies" className="flex items-center gap-2">
                  <Film size={16} />
                  Movies
                </TabsTrigger>
                <TabsTrigger value="shows" className="flex items-center gap-2">
                  <Monitor size={16} />
                  Shows
                </TabsTrigger>
                <TabsTrigger value="episodes" className="flex items-center gap-2">
                  <Calendar size={16} />
                  Episodes
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Controls Row */}
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center w-full lg:w-auto">
              {/* Data Source Toggle */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-400">Source:</span>
                <ToggleGroup 
                  type="single" 
                  defaultValue="tmdb" 
                  value={dataSource} 
                  onValueChange={(value) => value && setDataSource(value as 'tmdb' | 'vidsrc')}
                  className="border rounded-md"
                >
                  <ToggleGroupItem value="tmdb" aria-label="Use TMDb API">TMDb</ToggleGroupItem>
                  <ToggleGroupItem value="vidsrc" aria-label="Use VidSrc API">VidSrc</ToggleGroupItem>
                </ToggleGroup>
              </div>

              {/* Domain Selection */}
              {dataSource === 'vidsrc' && (
                <div className="flex items-center gap-2">
                  <Globe size={16} className="text-gray-400" />
                  <Select value={selectedDomain || 'auto'} onValueChange={handleDomainChange}>
                    <SelectTrigger className="w-40 bg-[#1A1A1E] border-[#2A2A2E]">
                      <SelectValue placeholder="Domain" />
                    </SelectTrigger>
                    <SelectContent className="bg-[#1A1A1E] border-[#2A2A2E]">
                      <SelectItem value="auto">Auto</SelectItem>
                      {VIDSRC_DOMAINS.map(domain => (
                        <SelectItem key={domain} value={domain}>
                          {domain}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Refresh Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={loading}
                className="border-[#2A2A2E] bg-transparent hover:bg-[#1A1A1E]"
              >
                <RotateCcw size={16} className={loading ? 'animate-spin' : ''} />
                Refresh
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-6 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Error State */}
          {error && (
            <Alert className="mb-6 border-red-500/30 bg-red-900/10">
              <AlertCircle className="h-4 w-4 text-red-400" />
              <AlertDescription className="text-red-400">
                <div className="flex items-center justify-between">
                  <span>{error}</span>
                  <Button variant="outline" size="sm" onClick={handleRefresh}>
                    Try Again
                  </Button>
                </div>
                <div className="mt-2">
                  <p className="text-sm">Try selecting a different data source or domain.</p>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Loading State */}
          {loading && (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
              {[...Array(10)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="aspect-[2/3] bg-[#1A1A1E] rounded-lg mb-3"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-[#1A1A1E] rounded w-3/4"></div>
                    <div className="h-3 bg-[#1A1A1E] rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Content Grid */}
          {!loading && currentItems.length > 0 && (
            <motion.div 
              className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              <AnimatePresence>
                {currentItems.map((item, index) => (
                  <motion.div
                    key={`${item.id}-${item.title || 'unknown'}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="group cursor-pointer"
                    onClick={() => handleContentClick(item)}
                  >
                    <div className="relative overflow-hidden rounded-lg aspect-[2/3] bg-[#1A1A1E] group-hover:ring-2 group-hover:ring-blue-500 transition-all duration-200">
                      {item.poster ? (
                        <Image
                          src={item.poster}
                          alt={item.title || 'Content'}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                          sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                          priority={false}
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-gray-500">
                          <Film size={48} />
                        </div>
                      )}
                      
                      {/* Overlay */}
                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                        <div className="text-center">
                          <Eye size={24} className="mx-auto mb-2" />
                          <p className="text-sm font-medium">Watch Now</p>
                        </div>
                      </div>
                    </div>
                    
                    {/* Content Info */}
                    <div className="mt-3 space-y-1">
                      <h3 className="font-medium text-sm line-clamp-2 group-hover:text-blue-400 transition-colors">
                        {item.title || 'Unknown Title'}
                      </h3>
                      <p className="text-xs text-gray-400">
                        {item.year || 'Unknown Year'}
                      </p>
                      
                      {/* Episode specific info */}
                      {activeTab === 'episodes' && 'season' in item && (
                        <p className="text-xs text-blue-400">
                          S{(item as VidSrcEpisode).season}:E{(item as VidSrcEpisode).episode}
                          {(item as VidSrcEpisode).show_title && ` • ${(item as VidSrcEpisode).show_title}`}
                        </p>
                      )}
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </motion.div>
          )}

          {/* Empty State */}
          {!loading && !error && currentItems.length === 0 && (
            <div className="text-center py-16">
              <Film size={64} className="mx-auto text-gray-600 mb-4" />
              <h3 className="text-xl font-medium mb-2">No {activeTab} found</h3>
              <p className="text-gray-400 mb-6">
                Try refreshing or selecting a different data source.
              </p>
              <Button onClick={handleRefresh} variant="outline">
                Refresh Content
              </Button>
            </div>
          )}

          {/* Enhanced Pagination */}
          {!loading && currentItems.length > 0 && (
            <div className="border-t border-vista-light/10 pt-8">
              <EnhancedPagination
                currentPage={page}
                totalPages={totalPages}
                onPageChange={setPage}
                isLoading={loading}
                compact={true}
                showPageInput={false}
                showQuickJump={false}
              />
            </div>
          )}

          {/* Data Source Info */}
          <div className="mt-8 text-center">
            <p className="text-xs text-gray-500">
              Currently using: <span className="text-gray-400 font-medium">{dataSource.toUpperCase()}</span>
              {dataSource === 'vidsrc' && selectedDomain && (
                <span> • Domain: <span className="text-gray-400 font-medium">{selectedDomain}</span></span>
              )}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 