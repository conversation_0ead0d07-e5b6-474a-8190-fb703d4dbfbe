'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowLeft, 
  Send, 
  Upload, 
  X, 
  AlertCircle, 
  Info,
  CreditCard,
  Settings,
  FileText,
  HelpCircle,
  Bug,
  Lightbulb
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';

// Icon mapping for dynamic categories
const iconMap = {
  CreditCard,
  Settings,
  FileText,
  HelpCircle,
  Bug,
  Lightbulb,
  Info
};

// Color mapping for dynamic categories
const colorMap = {
  '#3B82F6': 'bg-blue-500/20 text-blue-400',
  '#10B981': 'bg-green-500/20 text-green-400',
  '#8B5CF6': 'bg-purple-500/20 text-purple-400',
  '#F59E0B': 'bg-orange-500/20 text-orange-400',
  '#EF4444': 'bg-red-500/20 text-red-400',
  '#DC2626': 'bg-red-600/20 text-red-500',
  '#6B7280': 'bg-gray-500/20 text-gray-400'
};

// Fallback categories in case API fails
const fallbackCategories = [
  {
    id: 'subscription',
    name: 'Subscription & Billing',
    description: 'Questions about your subscription, billing, and payments',
    icon: CreditCard,
    color: 'bg-blue-500/20 text-blue-400'
  },
  {
    id: 'technical',
    name: 'Technical Support',
    description: 'Streaming issues, app problems, and technical difficulties',
    icon: Settings,
    color: 'bg-green-500/20 text-green-400'
  },
  {
    id: 'account',
    name: 'Account Management',
    description: 'Profile settings, password reset, and account security',
    icon: FileText,
    color: 'bg-purple-500/20 text-purple-400'
  },
  {
    id: 'content',
    name: 'Content & Features',
    description: 'Questions about shows, movies, and platform features',
    icon: HelpCircle,
    color: 'bg-orange-500/20 text-orange-400'
  },
  {
    id: 'bug_report',
    name: 'Report a Bug',
    description: 'Found something broken? Let us know so we can fix it',
    icon: Bug,
    color: 'bg-red-500/20 text-red-400'
  },
  {
    id: 'feature_request',
    name: 'Feature Request',
    description: 'Suggest new features or improvements to StreamVista',
    icon: Lightbulb,
    color: 'bg-yellow-500/20 text-yellow-400'
  }
];

const priorities = [
  { value: 'low', label: 'Low', description: 'General questions or minor issues' },
  { value: 'medium', label: 'Medium', description: 'Issues affecting your experience' },
  { value: 'high', label: 'High', description: 'Significant problems or urgent requests' },
  { value: 'urgent', label: 'Urgent', description: 'Critical issues requiring immediate attention' }
];

export default function NewTicketPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, isLoading } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [categories, setCategories] = useState(fallbackCategories);
  const [categoriesLoading, setCategoriesLoading] = useState(true);

  const [formData, setFormData] = useState({
    category: searchParams.get('category') || '',
    priority: 'medium',
    subject: '',
    description: '',
    includeSystemInfo: true,
    tags: [] as string[],
    attachments: [] as string[]
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Load categories from API
  const loadCategories = async () => {
    try {
      setCategoriesLoading(true);
      const response = await fetch('/api/help/categories');
      if (response.ok) {
        const data = await response.json();
        if (data.categories && data.categories.length > 0) {
          // Transform API categories to frontend format
          const transformedCategories = data.categories.map((cat: any) => ({
            id: cat.slug,
            name: cat.name,
            description: cat.description,
            icon: iconMap[cat.icon as keyof typeof iconMap] || Info,
            color: colorMap[cat.color as keyof typeof colorMap] || 'bg-gray-500/20 text-gray-400'
          }));
          setCategories(transformedCategories);
        }
      } else {
        console.warn('Failed to load categories from API, using fallback');
      }
    } catch (error) {
      console.error('Error loading categories:', error);
      toast.error('Failed to load categories, using defaults');
    } finally {
      setCategoriesLoading(false);
    }
  };

  // Check authentication
  useEffect(() => {
    if (!isLoading && !user) {
      toast.error('Please sign in to create a support ticket');
      router.push('/auth');
    }
  }, [user, isLoading, router]);

  // Load categories on component mount
  useEffect(() => {
    loadCategories();
  }, []);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.category) newErrors.category = 'Please select a category';
    if (!formData.subject.trim()) newErrors.subject = 'Subject is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (formData.description.trim().length < 10) {
      newErrors.description = 'Please provide more details (at least 10 characters)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const testAuth = async () => {
    try {
      const response = await fetch(`/api/help/debug?userId=${user?.id}`, {
        credentials: 'include'
      });
      const data = await response.json();
      console.log('Auth test result:', data);
      toast.success(`Auth test: ${data.authenticated ? 'Success' : 'Failed'} - ${data.message}`);
    } catch (error) {
      console.error('Auth test error:', error);
      toast.error('Auth test failed');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the errors before submitting');
      return;
    }

    setIsSubmitting(true);

    try {
      const metadata: any = {};
      
      if (formData.includeSystemInfo) {
        metadata.userAgent = navigator.userAgent;
        metadata.browserInfo = `${navigator.userAgent}`;
        metadata.deviceInfo = `Screen: ${screen.width}x${screen.height}, Platform: ${navigator.platform}`;
      }

      const response = await fetch('/api/help/tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies in the request
        body: JSON.stringify({
          userId: user?.id, // Include userId in request body
          category: formData.category,
          priority: formData.priority,
          subject: formData.subject.trim(),
          description: formData.description.trim(),
          tags: formData.tags,
          attachments: formData.attachments,
          metadata
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create ticket');
      }

      const data = await response.json();
      toast.success('Support ticket created successfully!');
      router.push(`/help/tickets/${data.ticket._id}`);
    } catch (error) {
      console.error('Error creating ticket:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create ticket');
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedCategory = categories.find(cat => cat.id === formData.category);

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-vista-dark text-vista-light">
        <Navbar />
        <div className="container mx-auto max-w-4xl py-16 px-4 text-center">
          <div className="animate-pulse">
            <div className="h-8 bg-vista-light/20 rounded w-1/3 mx-auto mb-4"></div>
            <div className="h-4 bg-vista-light/20 rounded w-1/2 mx-auto"></div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // Don't render if not authenticated (will redirect)
  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-vista-dark text-vista-light">
      <Navbar />
      
      {/* Header */}
      <section className="py-16 px-4">
        <div className="container mx-auto max-w-4xl">
          <div className="flex items-center gap-4 mb-8">
            <Link href="/help/tickets">
              <Button variant="outline" size="sm" className="border-vista-light/20 text-vista-light hover:bg-vista-light/10">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Tickets
              </Button>
            </Link>
            <div>
              <h1 className="text-4xl font-bold text-vista-light">Create Support Ticket</h1>
              <p className="text-vista-light/70">Get help from our support team</p>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Category Selection */}
            <Card className="bg-vista-card border-vista-light/10">
              <CardHeader>
                <CardTitle className="text-vista-light">What can we help you with?</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4">
                  {categories.map((category) => (
                    <div
                      key={category.id}
                      className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                        formData.category === category.id
                          ? 'border-vista-blue bg-vista-blue/10'
                          : 'border-vista-light/10 hover:border-vista-light/20'
                      }`}
                      onClick={() => handleInputChange('category', category.id)}
                    >
                      <div className="flex items-center gap-3 mb-2">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${category.color}`}>
                          <category.icon className="w-4 h-4" />
                        </div>
                        <h3 className="font-semibold text-vista-light">{category.name}</h3>
                      </div>
                      <p className="text-sm text-vista-light/70">{category.description}</p>
                    </div>
                  ))}
                </div>
                {errors.category && (
                  <p className="text-red-400 text-sm mt-2 flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {errors.category}
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Priority Selection */}
            <Card className="bg-vista-card border-vista-light/10">
              <CardHeader>
                <CardTitle className="text-vista-light">Priority Level</CardTitle>
              </CardHeader>
              <CardContent>
                <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                  <SelectTrigger className="bg-vista-dark border-vista-light/20 text-vista-light">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-vista-dark border-vista-light/20">
                    {priorities.map((priority) => (
                      <SelectItem key={priority.value} value={priority.value} className="text-vista-light">
                        <div>
                          <div className="font-medium">{priority.label}</div>
                          <div className="text-sm text-vista-light/70">{priority.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </CardContent>
            </Card>

            {/* Subject and Description */}
            <Card className="bg-vista-card border-vista-light/10">
              <CardHeader>
                <CardTitle className="text-vista-light">Ticket Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label htmlFor="subject" className="text-vista-light">Subject *</Label>
                  <Input
                    id="subject"
                    value={formData.subject}
                    onChange={(e) => handleInputChange('subject', e.target.value)}
                    placeholder="Brief summary of your issue or question"
                    className="bg-vista-dark border-vista-light/20 text-vista-light mt-2"
                  />
                  {errors.subject && (
                    <p className="text-red-400 text-sm mt-1 flex items-center gap-1">
                      <AlertCircle className="w-4 h-4" />
                      {errors.subject}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="description" className="text-vista-light">Description *</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Please provide detailed information about your issue, including steps to reproduce if applicable..."
                    className="bg-vista-dark border-vista-light/20 text-vista-light mt-2 min-h-[120px]"
                  />
                  <div className="flex justify-between items-center mt-1">
                    {errors.description ? (
                      <p className="text-red-400 text-sm flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.description}
                      </p>
                    ) : (
                      <p className="text-vista-light/60 text-sm">
                        {formData.description.length}/1000 characters
                      </p>
                    )}
                  </div>
                </div>

                {/* System Information */}
                <div className="flex items-start gap-3 p-4 bg-vista-dark/50 rounded-lg">
                  <Checkbox
                    id="includeSystemInfo"
                    checked={formData.includeSystemInfo}
                    onCheckedChange={(checked) => handleInputChange('includeSystemInfo', checked)}
                  />
                  <div>
                    <Label htmlFor="includeSystemInfo" className="text-vista-light cursor-pointer">
                      Include system information
                    </Label>
                    <p className="text-sm text-vista-light/70 mt-1">
                      This helps our support team diagnose technical issues faster by including your browser, device, and system details.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Selected Category Info */}
            {selectedCategory && (
              <Card className="bg-gradient-to-r from-vista-blue/10 to-vista-accent/10 border-vista-blue/20">
                <CardContent className="p-6">
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${selectedCategory.color}`}>
                      <selectedCategory.icon className="w-4 h-4" />
                    </div>
                    <h3 className="font-semibold text-vista-light">Selected: {selectedCategory.name}</h3>
                  </div>
                  <p className="text-vista-light/80 text-sm">{selectedCategory.description}</p>
                </CardContent>
              </Card>
            )}

            {/* Submit Button */}
            <div className="flex justify-end gap-4">
              <Button
                type="button"
                onClick={testAuth}
                variant="outline"
                className="border-yellow-500/20 text-yellow-400 hover:bg-yellow-500/10"
              >
                Test Auth
              </Button>
              <Link href="/help/tickets">
                <Button variant="outline" className="border-vista-light/20 text-vista-light hover:bg-vista-light/10">
                  Cancel
                </Button>
              </Link>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-vista-blue hover:bg-vista-blue/90"
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin mr-2" />
                    Creating Ticket...
                  </>
                ) : (
                  <>
                    <Send className="w-4 h-4 mr-2" />
                    Create Ticket
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </section>

      <Footer />
    </div>
  );
}
