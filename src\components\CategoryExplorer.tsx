'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
// Import the getTMDbImageUrl function
import { getTMDbImageUrl } from '@/lib/tmdb-api';
// Removed Button import as it's not used
// import { useLanguage } from '@/lib/i18n/LanguageContext';

interface ImageItem {
  id: number;
  title: string;
  posterPath: string | null;
  backdropPath: string | null;
}

interface Category {
  name: string;
  images: ImageItem[];
  link: string;
  description: string;
  currentImageIndex: number;
}

// Type for the API response structure
interface ApiCategoryResponse {
  [categoryName: string]: {
    images: ImageItem[];
  };
}

// Reduce image count
const IMAGE_FETCH_COUNT = 5;

// Initial categories structure
const initialCategoriesData: Omit<Category, 'images' | 'currentImageIndex'>[] = [
  {
    name: 'Movies',
    link: '/movies',
    description: 'Browse our extensive movie collection'
  },
  {
    name: 'TV Shows',
    link: '/shows',
    description: 'Discover popular and trending TV series'
  },
  {
    name: 'Action',
    link: '/movies?genre=action',
    description: 'High-octane action and adventure films'
  },
  {
    name: 'Sci-Fi',
    link: '/movies?genre=sci-fi',
    description: 'Futuristic and mind-bending sci-fi content'
  },
  {
    name: 'Horror',
    link: '/movies?genre=horror',
    description: 'Spine-chilling horror and thriller films'
  },
  {
    name: 'Documentary',
    link: '/shows?genre=documentary',
    description: 'Fascinating real-world documentary series'
  },
  {
    name: 'Animation',
    link: '/movies?genre=animation',
    description: 'Animated features for viewers of all ages'
  },
  {
    name: 'Drama',
    link: '/shows?genre=drama',
    description: 'Compelling dramatic series and films'
  },
];

// Simplified variants, especially for mobile
const containerVariants = {
  hidden: { opacity: 1 }, // Start visible on mobile
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  show: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4
    }
  }
};

// Keep fallbacks
const fallbackImages = {
  'Movies': 'https://images.pexels.com/photos/33129/popcorn-movie-party-entertainment.jpg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  'TV Shows': 'https://images.pexels.com/photos/5699456/pexels-photo-5699456.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  'Action': 'https://images.pexels.com/photos/1619317/pexels-photo-1619317.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  'Sci-Fi': 'https://images.pexels.com/photos/3262249/pexels-photo-3262249.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  'Horror': 'https://images.pexels.com/photos/5487134/pexels-photo-5487134.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  'Documentary': 'https://images.pexels.com/photos/7294527/pexels-photo-7294527.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  'Animation': 'https://images.pexels.com/photos/3227984/pexels-photo-3227984.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  'Drama': 'https://images.pexels.com/photos/256417/pexels-photo-256417.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
};

const DEFAULT_FALLBACK_IMAGE = '/images/content/placeholder.jpg';

export default function CategoryExplorer() {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  // Remove activeIndex state
  const [isMobile, setIsMobile] = useState(false);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null); // For desktop hover image change

  // Detect mobile devices
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Helper function to get proper image URL
  const getImageUrl = useCallback((path: string | null): string => {
    if (!path || path === 'null' || path === 'undefined' || path.trim() === '') {
      return 'https://placehold.co/300x450/171717/CCCCCC?text=No+Image';
    }

    // If it's already a full URL (like our fallback images), return it directly
    if (path.startsWith('http')) {
      return path;
    }

    // Otherwise, use the TMDB image URL helper
    // Handle potential // in path from TMDB
    const cleanedPath = path.startsWith('//') ? path.substring(1) : path;
    return getTMDbImageUrl(cleanedPath);
  }, []);

  // Fetch images for all categories in one go
  useEffect(() => {
    const fetchAllCategoryImages = async () => {
      setIsLoading(true);
      const categoryNames = initialCategoriesData.map(cat => cat.name).join(',');
      try {
        const response = await fetch(`/api/category-images?category=${encodeURIComponent(categoryNames)}&count=${IMAGE_FETCH_COUNT}`);
        if (!response.ok) throw new Error(`API error: ${response.statusText}`);

        const apiData: ApiCategoryResponse = await response.json();

        if (!apiData || typeof apiData !== 'object') {
           throw new Error('Invalid API response format');
        }

        const categoriesWithImages = initialCategoriesData.map(initialCat => {
          const categoryData = apiData[initialCat.name];
          let images: ImageItem[] = [];

          if (categoryData && Array.isArray(categoryData.images) && categoryData.images.length > 0) {
            images = categoryData.images;
          } else {
            console.warn(`No valid images found for ${initialCat.name}, using fallback.`);
            const fallbackUrl = fallbackImages[initialCat.name as keyof typeof fallbackImages] || DEFAULT_FALLBACK_IMAGE;
            images = [{ id: 0, title: 'Fallback Image', posterPath: fallbackUrl, backdropPath: fallbackUrl }];
          }

          return {
            ...initialCat,
            images: images,
            currentImageIndex: 0
          };
        });

        setCategories(categoriesWithImages);
      } catch (error) {
        console.error('Error fetching category images:', error);
        // Fallback: Populate categories with static fallback images if API fails
        const categoriesWithFallbacks = initialCategoriesData.map(category => ({
          ...category,
          images: [{
            id: 0,
            title: category.name,
            posterPath: fallbackImages[category.name as keyof typeof fallbackImages] || DEFAULT_FALLBACK_IMAGE,
            backdropPath: fallbackImages[category.name as keyof typeof fallbackImages] || DEFAULT_FALLBACK_IMAGE
          }],
          currentImageIndex: 0
        }));
        setCategories(categoriesWithFallbacks);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAllCategoryImages();
  }, []); // Fetch only once

  // Handle hover image change (Desktop only)
  const handleMouseEnter = (index: number) => {
    if (isMobile) return;
    setHoveredIndex(index);
    // Debounce image change on hover start
    if (hoverTimeoutRef.current) clearTimeout(hoverTimeoutRef.current);
    hoverTimeoutRef.current = setTimeout(() => {
        changeCategoryImage(index);
    }, 200); // Small delay before changing image
  };

  const handleMouseLeave = () => {
    if (isMobile) return;
    setHoveredIndex(null);
    if (hoverTimeoutRef.current) clearTimeout(hoverTimeoutRef.current);
  };

  // Function to change image for a specific category (used on desktop hover)
  const changeCategoryImage = (index: number) => {
      setCategories(prev => {
          const updated = [...prev];
          const category = updated[index];
          if (category && category.images.length > 1) {
              let newIndex;
              do {
                  newIndex = Math.floor(Math.random() * category.images.length);
              } while (newIndex === category.currentImageIndex && category.images.length > 1);
              updated[index] = { ...category, currentImageIndex: newIndex };
          }
          return updated;
      });
  };

  if (isLoading) {
    return (
      <section className="py-8">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-vista-light">
              Explore Content
            </h2>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Array.from({ length: 8 }).map((_, index) => (
              <div
                key={index}
                className="relative overflow-hidden rounded-lg aspect-[16/9] bg-vista-dark/50 animate-pulse"
              />
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-8">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-vista-light">
            Explore Content
          </h2>
        </div>

        {/* Conditionally apply motion variants */}
        <motion.div
          variants={!isMobile ? containerVariants : undefined}
          initial={!isMobile ? "hidden" : undefined}
          animate={!isMobile ? "show" : undefined}
          className="grid grid-cols-2 md:grid-cols-4 gap-4"
        >
          {categories.map((category, index) => (
            <Link href={category.link} key={category.name} legacyBehavior>
              <motion.a
                variants={!isMobile ? itemVariants : undefined}
                className="relative block overflow-hidden rounded-lg aspect-[16/9] group shadow-lg transition-transform duration-300 ease-in-out hover:scale-[1.03]"
                onMouseEnter={() => handleMouseEnter(index)}
                onMouseLeave={handleMouseLeave}
              >
                {/* Background Image Container */}
                <div className="absolute inset-0 bg-gray-800">
                  {/* Mobile: Render only the first image */}
                  {isMobile && category.images.length > 0 && (
                    <Image
                      key={`${category.name}-mobile-${category.images[0].id}`}
                      src={getImageUrl(category.images[0].backdropPath || category.images[0].posterPath)}
                      alt={category.name}
                      fill
                      sizes="(max-width: 768px) 50vw, 25vw"
                      quality={75}
                      priority={index < 4}
                      className="object-cover"
                      onError={(e) => { e.currentTarget.src = DEFAULT_FALLBACK_IMAGE; }}
                    />
                  )}
                  {/* Desktop: Render current image with transition */}
                  {!isMobile && category.images.map((image, imageIndex) => (
                    <Image
                      key={`${category.name}-desktop-${image.id}-${imageIndex}`}
                      src={getImageUrl(image.backdropPath || image.posterPath)}
                      alt={`${category.name} background`}
                      fill
                      sizes="(max-width: 768px) 50vw, 25vw"
                      quality={80}
                      priority={index < 4 && imageIndex === 0}
                      className={`object-cover absolute inset-0 transition-opacity duration-700 ease-in-out ${ imageIndex === category.currentImageIndex ? 'opacity-100' : 'opacity-0' }`}
                      onError={(e) => { e.currentTarget.src = DEFAULT_FALLBACK_IMAGE; }}
                    />
                  ))}
                </div>

                {/* Gradient Overlay (simplified) */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />

                {/* Text Content */}
                <div className="absolute inset-0 flex flex-col justify-end p-3 md:p-4">
                  <h3 className="text-lg md:text-xl font-semibold text-white mb-0.5 md:mb-1">
                    {category.name}
                  </h3>
                  <p className="text-xs md:text-sm text-gray-200 line-clamp-2 opacity-90">
                    {category.description}
                  </p>
                </div>

                {/* Simple Border on Hover (optional) */}
                {!isMobile && (
                    <div className="absolute inset-0 border-2 border-transparent rounded-lg group-hover:border-vista-blue/50 transition-colors duration-300"></div>
                )}
              </motion.a>
            </Link>
          ))}
        </motion.div>
      </div>
    </section>
  );
}