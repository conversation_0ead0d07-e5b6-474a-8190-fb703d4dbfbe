'use client';

import { useEffect, useMemo, useState, useCallback, Suspense, lazy } from 'react';
import { useLanguage } from '@/lib/i18n/LanguageContext';
import ContentRow from '@/components/ContentRow';
import InfiniteContentRow from '@/components/InfiniteContentRow';
import CategoryExplorer from '@/components/CategoryExplorer';
import PersonalizedRecommendations from '@/components/PersonalizedRecommendations';
import InfiniteRecommendations from '@/components/InfiniteRecommendations';
import RecommendedSection from '@/components/RecommendedSection';
import ProfilesShowcase from '@/components/ProfilesShowcase';
import TrendingTrailer from '@/components/TrendingTrailer';
import FeaturedHero from '@/components/FeaturedHero';
// import StreamingFeatures from '@/components/StreamingFeatures'; // Commented out - Module potentially missing
import NewsletterSubscription from '@/components/newsletter/NewsletterSubscription';
import { Navbar } from '@/components/Navbar';
import Footer from '@/components/Footer';
import BannerDisplay from '@/components/BannerDisplay';
import { motion } from 'framer-motion';
import { ContentType } from '@/data/content';
import { formatTMDbContentForCards, ContentCardType } from '@/lib/content-utils';
import { getPopularMovies, getTopRatedMovies, getTrendingDaily, getPopularTVShows, getTopRatedTVShows, MappedContent } from '@/lib/tmdb-api';
import { Loader2 } from 'lucide-react';
import TMDbAttribution from '@/components/TMDbAttribution';
// import BrowseByGenre from '@/components/BrowseByGenre'; // Commented out - Module potentially missing
import { Separator } from '@/components/ui/separator';
import { Button } from "@/components/ui/button";

// Define the props structure based on the data fetched in the server component
interface HomePageClientContentProps {
  initialContent: {
    popularMovies: MappedContent[];
    topRatedMovies: MappedContent[];
    popularShows: MappedContent[];
    topRatedShows: MappedContent[];
    trending: MappedContent[];
    continueWatching: ContentCardType[];
  };
}

export default function HomePageClientContent({ initialContent }: HomePageClientContentProps) {
  const { t } = useLanguage();
  const [isLoading, setIsLoading] = useState(false); // Initial data is already loaded by server
  const [isMobile, setIsMobile] = useState(false);
  const [isLowEndDevice, setIsLowEndDevice] = useState(false);

  // Note: The initialContent prop contains the data fetched by the Server Component.
  // We don't need separate states for each content type here unless we modify them client-side later.
  // The InfiniteContentRow components will handle their own subsequent fetching.

  // Detect mobile and low-end devices (remains client-side)
  useEffect(() => {
    const checkDevice = () => {
      const mobile = window.innerWidth < 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      setIsMobile(mobile);

      let isLowEnd = false;
      if (mobile) {
        try {
          const mem = 'deviceMemory' in navigator ? (navigator as any).deviceMemory : 4;
          const cores = 'hardwareConcurrency' in navigator ? (navigator as any).hardwareConcurrency : 4;
          isLowEnd = (mem < 4 || cores < 4);
        } catch (e) {
          console.warn("Could not detect device specs for low-end check.");
        }
      }
      setIsLowEndDevice(isLowEnd);
    };
    checkDevice();
    window.addEventListener('resize', checkDevice);
    return () => window.removeEventListener('resize', checkDevice);
  }, []);

  // We no longer need the main data fetching useEffect here, as data comes via props.

  // We don't need useMemo for initial data formatting if we pass formatted data directly,
  // but InfiniteContentRow still needs its fetchMoreContents which uses the utils.

  return (
    <div className="min-h-screen bg-vista-dark">
      {/* Navbar and Footer are included here because they often contain client-side logic */}
      <Navbar />
      <main className="min-h-screen bg-vista-dark pt-16 md:pt-0">
        {/* Top Banner Ads */}
        <div className="px-4 sm:px-6 lg:px-8 pt-4 md:pt-20">
          <BannerDisplay position="top" className="mb-4" />
        </div>

        {/* Hero Overlay Banner Ads */}
        <div className="relative">
          {!isLowEndDevice && <FeaturedHero />}
          <div className="absolute top-4 left-4 right-4 z-10">
            <BannerDisplay position="hero-overlay" className="mb-4" />
          </div>
        </div>

        {/* Simplified Hero for low-end devices */}
        {isLowEndDevice && (
          <div className="hero-section relative w-full overflow-hidden pt-16 h-[40vh] flex items-center justify-center bg-vista-dark">
            <div className="text-center px-4">
              <h1 className="text-3xl font-bold text-white mb-4">Welcome to StreamVista</h1>
              <p className="text-vista-light/80 mb-6">Discover movies and shows tailored for you</p>
              <Button className="bg-vista-blue hover:bg-vista-blue/90">Explore Content</Button>
            </div>
          </div>
        )}

        {/* Main Content - disable animations on mobile */}
        <div className="mt-4 md:mt-8">
          {/* Initial loading state is handled by Suspense in the Server Component if needed */}
          {/* We can assume initialContent is available here */}
            <>
              {/* Personalized Recommendations */}
              {initialContent.trending.length > 0 && (
                <div className="mb-8">
                  <PersonalizedRecommendations />
                </div>
              )}

              {/* Continue Watching - Uses initial prop data */}
              {initialContent.continueWatching.length > 0 && !isLowEndDevice && (
                <div className="mb-8">
                  <InfiniteContentRow
                    title="Continue Watching"
                    initialContents={initialContent.continueWatching} // Already ContentCardType[] from server
                    seeAllLink="/continue"
                    // fetchMoreContents is omitted (optional)
                  />
                </div>
              )}

              {/* Trending Now */}
              {initialContent.trending.length > 0 && (
                  <div className="mb-8">
                    <InfiniteContentRow
                      title="Trending Now"
                      initialContents={formatTMDbContentForCards(initialContent.trending)}
                      // fetchMoreContents remains client-side as it fetches subsequent pages
                      fetchMoreContents={async (page: number): Promise<ContentCardType[]> => {
                         const mappedData: MappedContent[] = await getTrendingDaily('all', page);
                         return formatTMDbContentForCards(mappedData);
                      }}
                      seeAllLink="/trending"
                    />
                  </div>
              )}

              {/* Render TrendingTrailer section */}
              <div className="mb-8">
                <TrendingTrailer />
              </div>

              {/* Top Rated Movies */}
              {initialContent.topRatedMovies.length > 0 && (
                 <div className="mb-8">
                     <InfiniteContentRow
                       title="Top Rated Movies"
                       initialContents={formatTMDbContentForCards(initialContent.topRatedMovies)}
                       fetchMoreContents={async (page: number): Promise<ContentCardType[]> => {
                          const mappedData: MappedContent[] = await getTopRatedMovies(page);
                          return formatTMDbContentForCards(mappedData);
                       }}
                       seeAllLink="/discover?category=top-rated-movies"
                     />
                 </div>
               )}

              {/* Category Explorer */}
              {!isLowEndDevice && initialContent.popularMovies.length > 0 && (
                <div className="mb-8">
                  <CategoryExplorer />
                </div>
              )}

              {/* Popular Movies */}
              {initialContent.popularMovies.length > 0 && (
                <div className="mb-8">
                  <InfiniteContentRow
                    title="Popular Movies"
                    initialContents={formatTMDbContentForCards(initialContent.popularMovies)}
                    fetchMoreContents={async (page: number): Promise<ContentCardType[]> => {
                      const mappedData: MappedContent[] = await getPopularMovies(page);
                      return formatTMDbContentForCards(mappedData);
                    }}
                    seeAllLink="/discover?category=popular-movies"
                  />
                </div>
              )}

              {/* Center Banner Ads */}
              <div className="px-4 sm:px-6 lg:px-8 my-8">
                <BannerDisplay position="center" className="mb-8" />
              </div>

              {/* Between Sections Banner Ads */}
              <div className="px-4 sm:px-6 lg:px-8 my-8">
                <BannerDisplay position="between-sections" className="mb-8" />
              </div>

              {/* Separator */}
              <Separator className="my-8 bg-vista-dark-blue/50" />

              {/* Popular TV Shows */}
              {initialContent.popularShows.length > 0 && (
                 <div className="mb-8">
                   <InfiniteContentRow
                     title="Popular TV Shows"
                     initialContents={formatTMDbContentForCards(initialContent.popularShows)}
                     fetchMoreContents={async (page: number): Promise<ContentCardType[]> => {
                        const mappedData: MappedContent[] = await getPopularTVShows(page);
                        return formatTMDbContentForCards(mappedData);
                     }}
                     seeAllLink="/discover?category=popular-shows"
                   />
                 </div>
              )}

              {/* Top Rated TV Shows */}
              {initialContent.topRatedShows.length > 0 && (
                <div className="mb-8">
                  <InfiniteContentRow
                    title="Top Rated TV Shows"
                    initialContents={formatTMDbContentForCards(initialContent.topRatedShows)}
                    fetchMoreContents={async (page: number): Promise<ContentCardType[]> => {
                       const mappedData: MappedContent[] = await getTopRatedTVShows(page);
                       return formatTMDbContentForCards(mappedData);
                    }}
                    seeAllLink="/discover?category=top-rated-shows"
                  />
                </div>
              )}

              {/* TMDb Attribution */}
              <div className="py-8 flex justify-center">
                <TMDbAttribution />
              </div>

              {/* Bottom Banner Ads */}
              <div className="px-4 sm:px-6 lg:px-8 mb-8">
                <BannerDisplay position="bottom" className="mb-8" />
              </div>

              {/* Newsletter Subscription */}
              {!isLowEndDevice && (
                <div className="mt-4 mb-12">
                  <NewsletterSubscription />
                </div>
              )}

              {/* Commented out sections remain commented */}
            </>
        </div>
      </main>
      <Footer />
    </div>
  );
} 