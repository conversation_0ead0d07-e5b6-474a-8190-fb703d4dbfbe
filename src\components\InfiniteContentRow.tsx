"use client";

import { useState, useRef, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { ChevronRight, ArrowRight, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ContentCard from './ContentCard';
import { ContentCardType } from '@/lib/content-utils';
import { useInView } from 'react-intersection-observer';

interface InfiniteContentRowProps {
  title: string;
  subtitle?: string;
  seeAllLink?: string;
  initialContents: ContentCardType[];
  fetchMoreContents?: (page: number) => Promise<ContentCardType[]>;
  itemsPerPage?: number;
  maxItems?: number;
}

export default function InfiniteContentRow({
  title,
  subtitle,
  seeAllLink,
  initialContents,
  fetchMoreContents,
  itemsPerPage = 10,
  maxItems = 50
}: InfiniteContentRowProps) {
  const rowRef = useRef<HTMLDivElement>(null);
  const [showControls, setShowControls] = useState(false);
  const [showLeftButton, setShowLeftButton] = useState(false);
  const [showRightButton, setShowRightButton] = useState(true);
  const [isMounted, setIsMounted] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const scrollTimeout = useRef<NodeJS.Timeout | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [isLowEndDevice, setIsLowEndDevice] = useState(false);

  const [contents, setContents] = useState<ContentCardType[]>(initialContents);
  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(
    !!fetchMoreContents && initialContents.length >= itemsPerPage && initialContents.length < maxItems
  );

  const { ref: inViewRef, inView } = useInView({
    threshold: 0.1,
    triggerOnce: false,
    rootMargin: '0px 200px 0px 0px'
  });

  const loadMore = useCallback(async () => {
    console.log(`[InfiniteScroll ${title}] Attempting loadMore. isLoading: ${isLoading}, hasMore: ${hasMore}`);
    if (isLoading || !hasMore || !fetchMoreContents) return;

    setIsLoading(true);
    const nextPage = page + 1;
    console.log(`[InfiniteScroll ${title}] Fetching page ${nextPage}`);
    try {
      const newContents = await fetchMoreContents(nextPage);
      console.log(`[InfiniteScroll ${title}] Fetched ${newContents.length} items for page ${nextPage}`);
      const uniqueNewContents = newContents.filter(newItem => 
        !contents.some(existingItem => existingItem.id === newItem.id)
      );
      setContents((prev) => [...prev, ...uniqueNewContents]);
      setPage(nextPage);
      const moreAvailable = newContents.length === itemsPerPage && (contents.length + uniqueNewContents.length) < maxItems;
      setHasMore(moreAvailable);
      console.log(`[InfiniteScroll ${title}] Setting hasMore to ${moreAvailable}`);
    } catch (error) {
      console.error(`[InfiniteScroll ${title}] Error fetching page ${nextPage}:`, error);
      setHasMore(false);
    } finally {
      setTimeout(() => setIsLoading(false), 300);
    }
  }, [isLoading, hasMore, page, fetchMoreContents, itemsPerPage, maxItems, contents, title]);

  useEffect(() => {
    console.log(`[InfiniteScroll ${title}] inView changed: ${inView}, hasMore: ${hasMore}, isLoading: ${isLoading}`);
    if (inView && hasMore && !isLoading && fetchMoreContents) {
      console.log(`[InfiniteScroll ${title}] Triggering loadMore via inView`);
      loadMore();
    }
  }, [inView, hasMore, isLoading, loadMore, fetchMoreContents, title]);

  useEffect(() => {
    const checkDevice = () => {
      const mobile = window.innerWidth < 768 ||
                    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      setIsMobile(mobile);

      let isLowEnd = false;
      if (mobile) {
          try {
              const mem = 'deviceMemory' in navigator ? (navigator as any).deviceMemory : 4;
              const cores = 'hardwareConcurrency' in navigator ? (navigator as any).hardwareConcurrency : 4;
              isLowEnd = (mem < 4 || cores < 4);
          } catch (e) {
              console.warn("Could not detect device specs for low-end check.");
          }
      }
      setIsLowEndDevice(isLowEnd);
    };
    checkDevice();
  }, []);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (rowRef.current) {
      const { scrollWidth, clientWidth } = rowRef.current;
      const canScroll = scrollWidth > clientWidth;
      setShowControls(canScroll);
      setShowRightButton(canScroll && rowRef.current.scrollLeft < scrollWidth - clientWidth - 5);
    }
  }, [contents.length]);

  const handleScroll = useCallback(() => {
    if (rowRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = rowRef.current;
      setShowLeftButton(scrollLeft > 20);
      const maxScrollLeft = scrollWidth - clientWidth;
      setShowRightButton(Math.ceil(scrollLeft) < maxScrollLeft - 20);

      setIsScrolling(true);
      if (scrollTimeout.current) clearTimeout(scrollTimeout.current);
      scrollTimeout.current = setTimeout(() => setIsScrolling(false), 100);
    }
  }, []);

  const scrollLeft = () => {
    if (rowRef.current) {
      rowRef.current.scrollBy({ left: -rowRef.current.clientWidth * 0.8, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (rowRef.current) {
      rowRef.current.scrollBy({ left: rowRef.current.clientWidth * 0.8, behavior: 'smooth' });
    }
  };

  useEffect(() => {
    return () => {
      if (scrollTimeout.current) clearTimeout(scrollTimeout.current);
    };
  }, []);

  if (!isMounted) {
    return null;
  }

  return (
    <section className="py-6 md:py-8">
      <div className="container px-4 md:px-6 mx-auto">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl md:text-2xl font-semibold text-vista-light tracking-tight">
              {title}
            </h2>
            {subtitle && (
              <p className="text-sm text-vista-light/70 mt-1">{subtitle}</p>
            )}
          </div>
          {seeAllLink && (
            <Link href={seeAllLink}>
              <Button variant="ghost" size="sm" className="text-vista-light hover:text-vista-light/80 hover:bg-vista-dark-lighter">
                See All <ChevronRight className="ml-1 h-4 w-4" />
              </Button>
            </Link>
          )}
        </div>

        <div className="relative">
          {showControls && showLeftButton && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-black/60 text-vista-light hover:bg-black/80 rounded-full h-8 w-8 shadow-md"
              onClick={scrollLeft}
            >
              <ChevronRight className="h-5 w-5 rotate-180" />
            </Button>
          )}

          <div
            ref={rowRef}
            className="flex space-x-3 md:space-x-4 overflow-x-auto scrollbar-hide pb-1 -mx-1 px-1 pt-1"
            onScroll={handleScroll}
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {contents.map((content, index) => (
              (!isLowEndDevice || index < 8) && (
                <div
                  key={`${content.id}-${index}`}
                  className="flex-none w-32 sm:w-[170px] md:w-[170px] lg:w-[170px]"
                >
                  <ContentCard
                    id={content.id}
                    title={content.title}
                    imagePath={content.imagePath}
                    type={content.type}
                    year={content.year}
                    ageRating={content.ageRating}
                    index={index}
                    link={`/watch/${content.id}?forcePlay=true&contentType=${content.type === 'shows' ? 'show' : 'movie'}`}
                    isAwardWinning={content.isAwardWinning}
                    dataSource={content.dataSource}
                  />
                </div>
              )
            ))}

            {hasMore && (
              <div
                ref={inViewRef}
                className="flex-none w-32 sm:w-[170px] md:w-[170px] lg:w-[170px] flex items-center justify-center"
                style={{ height: '100px' }}
              >
                {isLoading && <Loader2 className="h-8 w-8 text-vista-blue animate-spin" />}
              </div>
            )}
          </div>

          {showControls && showRightButton && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-black/60 text-vista-light hover:bg-black/80 rounded-full h-8 w-8 shadow-md"
              onClick={scrollRight}
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          )}
        </div>
      </div>
    </section>
  );
}

