import { useState, useEffect } from 'react';
import Image from 'next/image';
import { getPersonImage } from '@/lib/image-utils';
import { getTMDbImageUrl } from '@/lib/tmdb-api';

// Helper function to get initials from a name
const getInitials = (name: string): string => {
  if (!name) return '?';

  // Split the name by spaces and get the first letter of each part
  const parts = name.split(' ').filter(part => part.length > 0);

  if (parts.length === 0) return '?';

  if (parts.length === 1) {
    // If only one part, return the first two letters or just the first if it's a single letter
    return parts[0].substring(0, Math.min(2, parts[0].length)).toUpperCase();
  }

  // Return the first letter of the first part and the first letter of the last part
  return (parts[0][0] + parts[parts.length - 1][0]).toUpperCase();
};

interface PersonImageProps {
  id: string;
  name: string;
  profilePath: string | null;
  gender?: number;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  priority?: boolean;
}

/**
 * Enhanced component for displaying actor/crew member images with advanced fallback
 * Uses multiple sources to ensure high image availability
 */
export default function PersonImage({
  id,
  name,
  profilePath,
  gender,
  size = 'md',
  className = '',
  priority = false
}: PersonImageProps) {
  // State for the image URL
  const [imageUrl, setImageUrl] = useState<string | null>(
    profilePath ? getTMDbImageUrl(profilePath, 'w342') : null // Increased size from w185 to w342
  );
  const [isLoading, setIsLoading] = useState(!imageUrl);
  const [hasError, setHasError] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string | null>(null);

  // Fixed size mappings (no dynamic classes)
  const sizeMap = {
    sm: { width: 32, height: 32, textSize: 'text-xs', className: 'w-8 h-8' },
    md: { width: 40, height: 40, textSize: 'text-sm', className: 'w-10 h-10' },
    lg: { width: 56, height: 56, textSize: 'text-base', className: 'w-14 h-14' }
  };

  const { width, height, textSize, className: sizeClassName } = sizeMap[size];

  // Log props on mount
  useEffect(() => {
    console.log(`[PersonImage] Component mounted for ${name} (ID: ${id})`, {
      id,
      name,
      profilePath,
      gender,
      size,
      priority
    });
  }, [id, name, profilePath, gender, size, priority]);

  // Fetch enhanced image if needed
  useEffect(() => {
    // Skip if we already have a TMDb image
    if (imageUrl) {
      console.log(`[PersonImage] Already have image URL for ${name}: ${imageUrl.substring(0, 50)}...`);
      return;
    }

    // Skip if we've already tried and failed for this person
    if (hasError) {
      console.log(`[PersonImage] Already tried and failed for ${name}`);
      return;
    }

    // Skip if no valid ID
    if (!id) {
      console.error(`[PersonImage] Missing ID for ${name}`);
      setDebugInfo("Missing ID");
      setHasError(true);
      return;
    }

    // Attempt to fetch enhanced image
    const fetchEnhancedImage = async () => {
      try {
        setIsLoading(true);
        console.log(`[PersonImage] Fetching image for ${name} (ID: ${id})`);
        setDebugInfo(`Fetching image for ${name} (ID: ${id})`);

        // Try to get image directly from TMDb first if we have a profile path
        if (profilePath) {
          console.log(`[PersonImage] Using TMDb image for ${name}: ${profilePath}`);
          const tmdbUrl = getTMDbImageUrl(profilePath, 'w342');
          setImageUrl(tmdbUrl);
          setDebugInfo(`Using TMDb image: ${profilePath}`);
          return;
        }

        // If no profile path, try enhanced sources
        console.log(`[PersonImage] No profile path for ${name}, trying enhanced sources`);
        const enhancedImageUrl = await getPersonImage(id, name, profilePath, gender);

        if (enhancedImageUrl) {
          console.log(`[PersonImage] Found enhanced image for ${name}: ${enhancedImageUrl.substring(0, 50)}...`);
          setImageUrl(enhancedImageUrl);
          setDebugInfo(`Found enhanced image from: ${
            enhancedImageUrl.includes('themoviedb.org') ? 'TMDb' :
            enhancedImageUrl.includes('wikimedia.org') ? 'Wikipedia' :
            enhancedImageUrl.startsWith('data:') ? 'Generated' : 'Other'
          }`);
        } else {
          console.warn(`[PersonImage] No image found for ${name}`);
          setHasError(true);
          setDebugInfo(`No image found for ${name}`);
        }
      } catch (error) {
        console.error(`[PersonImage] Error fetching image for ${name} (ID: ${id}):`, error);
        setHasError(true);
        setDebugInfo(`Error: ${error instanceof Error ? error.message : String(error)}`);
      } finally {
        setIsLoading(false);
      }
    };

    fetchEnhancedImage();
  }, [id, name, profilePath, gender, hasError, imageUrl]);

  // If we have an image URL, display the image
  if (imageUrl) {
    // Check for known problematic images and skip them
    const knownBadImages = [
      'LennyKuhr1.jpg',
      'https://upload.wikimedia.org/wikipedia/commons/L/Le/LennyKuhr1.jpg'
    ];

    // If this is a known bad image, skip directly to initials
    if (knownBadImages.some(badImage => imageUrl.includes(badImage))) {
      console.warn(`[PersonImage] Skipping known problematic image for ${name} (ID: ${id})`);

      // Render initials instead
      return (
        <div
          className={`${sizeClassName} rounded-full overflow-hidden flex-shrink-0 border-2 border-vista-dark-lighter shadow-sm ${className} bg-vista-dark-lighter flex items-center justify-center`}
          title={name}
        >
          <span className="text-vista-light font-medium">
            {getInitials(name)}
          </span>
        </div>
      );
    }

    // Sanitize the image URL to ensure it's valid
    let sanitizedImageUrl = imageUrl;

    // Handle Wikipedia/Wikimedia URLs that might have issues
    if (imageUrl.includes('wikimedia.org')) {
      try {
        // For Wikimedia URLs, use a direct TMDb fallback if available
        if (profilePath) {
          sanitizedImageUrl = `https://image.tmdb.org/t/p/w185${profilePath}`;
        } else {
          // Otherwise try our proxy, but with error handling
          sanitizedImageUrl = `/api/wiki-image?path=${encodeURIComponent(imageUrl)}`;
        }
      } catch (e) {
        console.warn(`[PersonImage] Error sanitizing Wikimedia URL for ${name}:`, e);
      }
    }

    return (
      <div
        className={`${sizeClassName} rounded-full overflow-hidden flex-shrink-0 border-2 border-vista-dark-lighter shadow-sm ${className}`}
        title={debugInfo || name}
      >
        <Image
          src={sanitizedImageUrl}
          alt={name}
          width={width}
          height={height}
          className="object-cover w-full h-full"
          priority={priority || size === 'lg'} // Prioritize loading larger images
          unoptimized={sanitizedImageUrl.startsWith('data:') || sanitizedImageUrl.includes('/api/wiki-image')} // Skip optimization for data URLs and proxied URLs
          onError={() => {
            // Log the error for debugging
            console.error(`[PersonImage] Image load error for ${name} (ID: ${id}): "${imageUrl}"`);

            // If image fails to load, show initials
            setImageUrl(null);
            setHasError(true);
            setDebugInfo(`Failed to load: ${imageUrl}`);
          }}
        />
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div
        className={`${sizeClassName} rounded-full flex-shrink-0 border-2 border-vista-dark-lighter shadow-sm bg-vista-dark/60 flex items-center justify-center animate-pulse ${className}`}
        title={`Loading image for ${name}...`}
      />
    );
  }

  // Fallback to initials
  return (
    <div
      className={`${sizeClassName} rounded-full bg-vista-blue/20 flex items-center justify-center flex-shrink-0 border-2 border-vista-dark-lighter shadow-sm ${className}`}
      title={debugInfo || `No image available for ${name}`}
    >
      <span className={textSize}>{name.charAt(0).toUpperCase()}</span>
    </div>
  );
}