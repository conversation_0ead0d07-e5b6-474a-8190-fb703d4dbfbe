'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import {
  Loader2,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Tag,
  Folder,
  FolderPlus
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  count: number;
}

interface Tag {
  id: string;
  name: string;
  slug: string;
  count: number;
}

export default function ContentCategories() {
  // State for categories and tags
  const [categories, setCategories] = useState<Category[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for category form
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [categoryForm, setCategoryForm] = useState({
    name: '',
    description: ''
  });

  // State for tag form
  const [isTagModalOpen, setIsTagModalOpen] = useState(false);
  const [editingTag, setEditingTag] = useState<Tag | null>(null);
  const [tagForm, setTagForm] = useState({
    name: ''
  });

  // State for form submission
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch categories and tags
  const fetchData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Fetch categories from API
      const categoriesResponse = await fetch('/api/admin/categories');
      if (!categoriesResponse.ok) {
        const errorData = await categoriesResponse.json();
        throw new Error(errorData.error || `Failed to fetch categories (${categoriesResponse.status})`);
      }
      const categoriesData = await categoriesResponse.json();

      // Fetch tags from API
      const tagsResponse = await fetch('/api/admin/tags');
      if (!tagsResponse.ok) {
        const errorData = await tagsResponse.json();
        throw new Error(errorData.error || `Failed to fetch tags (${tagsResponse.status})`);
      }
      const tagsData = await tagsResponse.json();

      setCategories(categoriesData.categories || []);
      setTags(tagsData.tags || []);
    } catch (error) {
      console.error('Error fetching data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, []);

  // Handle opening category modal
  const handleAddCategory = () => {
    setEditingCategory(null);
    setCategoryForm({ name: '', description: '' });
    setIsCategoryModalOpen(true);
  };

  // Handle editing category
  const handleEditCategory = (category: Category) => {
    setEditingCategory(category);
    setCategoryForm({
      name: category.name,
      description: category.description || ''
    });
    setIsCategoryModalOpen(true);
  };

  // Handle category form change
  const handleCategoryFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCategoryForm(prev => ({ ...prev, [name]: value }));
  };

  // Handle category form submission
  const handleCategorySubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!categoryForm.name.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Category name is required',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Generate slug from name if not provided
      const slug = categoryForm.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');

      if (editingCategory) {
        // Update existing category via API
        const response = await fetch(`/api/admin/categories/${editingCategory.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            name: categoryForm.name,
            slug,
            description: categoryForm.description
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to update category (${response.status})`);
        }

        const updatedCategory = await response.json();

        // Update local state
        setCategories(prev =>
          prev.map(cat =>
            cat.id === editingCategory.id ? updatedCategory : cat
          )
        );

        toast({
          title: 'Category Updated',
          description: `"${categoryForm.name}" has been updated successfully.`,
          variant: 'success'
        });
      } else {
        // Add new category via API
        const response = await fetch('/api/admin/categories', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            name: categoryForm.name,
            slug,
            description: categoryForm.description
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to create category (${response.status})`);
        }

        const newCategory = await response.json();

        // Update local state
        setCategories(prev => [...prev, newCategory]);

        toast({
          title: 'Category Added',
          description: `"${categoryForm.name}" has been added successfully.`,
          variant: 'success'
        });
      }

      // Close modal
      setIsCategoryModalOpen(false);
    } catch (error) {
      console.error('Error saving category:', error);
      toast({
        title: 'Error',
        description: 'Failed to save category',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle deleting category
  const handleDeleteCategory = async (categoryId: string) => {
    if (!confirm('Are you sure you want to delete this category?')) {
      return;
    }

    try {
      // Delete category via API
      const response = await fetch(`/api/admin/categories/${categoryId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to delete category (${response.status})`);
      }

      // Update local state
      setCategories(prev => prev.filter(cat => cat.id !== categoryId));

      toast({
        title: 'Category Deleted',
        description: 'The category has been deleted successfully.',
        variant: 'success'
      });
    } catch (error) {
      console.error('Error deleting category:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete category',
        variant: 'destructive'
      });
    }
  };

  // Handle opening tag modal
  const handleAddTag = () => {
    setEditingTag(null);
    setTagForm({ name: '' });
    setIsTagModalOpen(true);
  };

  // Handle editing tag
  const handleEditTag = (tag: Tag) => {
    setEditingTag(tag);
    setTagForm({ name: tag.name });
    setIsTagModalOpen(true);
  };

  // Handle tag form change
  const handleTagFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setTagForm(prev => ({ ...prev, [name]: value }));
  };

  // Handle tag form submission
  const handleTagSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!tagForm.name.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Tag name is required',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Generate slug from name
      const slug = tagForm.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');

      if (editingTag) {
        // Update existing tag via API
        const response = await fetch(`/api/admin/tags/${editingTag.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            name: tagForm.name,
            slug
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to update tag (${response.status})`);
        }

        const updatedTag = await response.json();

        // Update local state
        setTags(prev =>
          prev.map(tag =>
            tag.id === editingTag.id ? updatedTag : tag
          )
        );

        toast({
          title: 'Tag Updated',
          description: `"${tagForm.name}" has been updated successfully.`,
          variant: 'success'
        });
      } else {
        // Add new tag via API
        const response = await fetch('/api/admin/tags', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            name: tagForm.name,
            slug
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Failed to create tag (${response.status})`);
        }

        const newTag = await response.json();

        // Update local state
        setTags(prev => [...prev, newTag]);

        toast({
          title: 'Tag Added',
          description: `"${tagForm.name}" has been added successfully.`,
          variant: 'success'
        });
      }

      // Close modal
      setIsTagModalOpen(false);
    } catch (error) {
      console.error('Error saving tag:', error);
      toast({
        title: 'Error',
        description: 'Failed to save tag',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle deleting tag
  const handleDeleteTag = async (tagId: string) => {
    if (!confirm('Are you sure you want to delete this tag?')) {
      return;
    }

    try {
      // Delete tag via API
      const response = await fetch(`/api/admin/tags/${tagId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to delete tag (${response.status})`);
      }

      // Update local state
      setTags(prev => prev.filter(tag => tag.id !== tagId));

      toast({
        title: 'Tag Deleted',
        description: 'The tag has been deleted successfully.',
        variant: 'success'
      });
    } catch (error) {
      console.error('Error deleting tag:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete tag',
        variant: 'destructive'
      });
    }
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="categories">
        <TabsList className="mb-4">
          <TabsTrigger value="categories" className="flex items-center">
            <Folder className="mr-2 h-4 w-4" />
            Categories
          </TabsTrigger>
          <TabsTrigger value="tags" className="flex items-center">
            <Tag className="mr-2 h-4 w-4" />
            Tags
          </TabsTrigger>
        </TabsList>

        <TabsContent value="categories">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="text-vista-light">Content Categories</CardTitle>
                <CardDescription>
                  Organize your content with categories
                </CardDescription>
              </div>
              <Button onClick={handleAddCategory}>
                <FolderPlus className="mr-2 h-4 w-4" />
                Add Category
              </Button>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-vista-light/50" />
                </div>
              ) : error ? (
                <div className="text-center py-8 text-red-500">
                  <p>{error}</p>
                  <Button
                    variant="outline"
                    onClick={fetchData}
                    className="mt-4"
                  >
                    Try Again
                  </Button>
                </div>
              ) : (
                <div className="rounded-md border border-vista-light/10 overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Slug</TableHead>
                        <TableHead>Content Count</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {categories.length > 0 ? (
                        categories.map((category) => (
                          <TableRow key={category.id}>
                            <TableCell className="font-medium">{category.name}</TableCell>
                            <TableCell>{category.description || '-'}</TableCell>
                            <TableCell>
                              <Badge variant="outline">{category.slug}</Badge>
                            </TableCell>
                            <TableCell>{category.count}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleEditCategory(category)}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleDeleteCategory(category.id)}
                                  className="text-red-500 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-6 text-vista-light/70">
                            No categories found. Add your first category to get started.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tags">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="text-vista-light">Content Tags</CardTitle>
                <CardDescription>
                  Tag your content for better discoverability
                </CardDescription>
              </div>
              <Button onClick={handleAddTag}>
                <Plus className="mr-2 h-4 w-4" />
                Add Tag
              </Button>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-vista-light/50" />
                </div>
              ) : error ? (
                <div className="text-center py-8 text-red-500">
                  <p>{error}</p>
                  <Button
                    variant="outline"
                    onClick={fetchData}
                    className="mt-4"
                  >
                    Try Again
                  </Button>
                </div>
              ) : (
                <div className="rounded-md border border-vista-light/10 overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Slug</TableHead>
                        <TableHead>Content Count</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {tags.length > 0 ? (
                        tags.map((tag) => (
                          <TableRow key={tag.id}>
                            <TableCell className="font-medium">{tag.name}</TableCell>
                            <TableCell>
                              <Badge variant="outline">{tag.slug}</Badge>
                            </TableCell>
                            <TableCell>{tag.count}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleEditTag(tag)}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleDeleteTag(tag.id)}
                                  className="text-red-500 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={4} className="text-center py-6 text-vista-light/70">
                            No tags found. Add your first tag to get started.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Category Form Modal */}
      <Dialog open={isCategoryModalOpen} onOpenChange={setIsCategoryModalOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {editingCategory ? 'Edit Category' : 'Add New Category'}
            </DialogTitle>
            <DialogDescription>
              {editingCategory
                ? 'Update the category details below.'
                : 'Create a new category to organize your content.'}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleCategorySubmit} className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Category Name</Label>
              <Input
                id="name"
                name="name"
                value={categoryForm.name}
                onChange={handleCategoryFormChange}
                placeholder="Enter category name"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Input
                id="description"
                name="description"
                value={categoryForm.description}
                onChange={handleCategoryFormChange}
                placeholder="Enter category description"
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsCategoryModalOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Save className="mr-2 h-4 w-4" />
                )}
                {editingCategory ? 'Update Category' : 'Create Category'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Tag Form Modal */}
      <Dialog open={isTagModalOpen} onOpenChange={setIsTagModalOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {editingTag ? 'Edit Tag' : 'Add New Tag'}
            </DialogTitle>
            <DialogDescription>
              {editingTag
                ? 'Update the tag details below.'
                : 'Create a new tag to label your content.'}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleTagSubmit} className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="tagName">Tag Name</Label>
              <Input
                id="tagName"
                name="name"
                value={tagForm.name}
                onChange={handleTagFormChange}
                placeholder="Enter tag name"
                required
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsTagModalOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Save className="mr-2 h-4 w-4" />
                )}
                {editingTag ? 'Update Tag' : 'Create Tag'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
