'use client';

import React, { useMemo } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Users, Clock, Calendar } from 'lucide-react';
import {
  Bar<PERSON>hart as RechartsBarChart, // Rename to avoid conflict
  Bar,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';

// Define a type that can handle both our component's data format and the dashboard's format
interface ActivityData {
  date?: string;
  logins?: number;
  signups?: number;
  // Dashboard data format
  type?: string;
  message?: string;
  details?: string;
  timestamp?: string;
  action?: string;
}

interface UserActivityChartProps {
  data: ActivityData[];
}

export function UserActivityChart({ data = [] }: UserActivityChartProps) {
  // Convert incoming data to our component's expected format if needed
  const processedData = useMemo(() => {
    // If no data, return empty array
    if (!data || data.length === 0) {
      console.log('No activity data provided to UserActivityChart');
      return [];
    }

    console.log(`Processing ${data.length} activity items for chart`);

    // First, let's count how many login and signup events we have
    let loginCount = 0;
    let signupCount = 0;

    const processedItems = data.map(item => {
      // If it's already in our format with explicit login/signup counts, use it directly
      if (item.logins !== undefined || item.signups !== undefined) {
        loginCount += item.logins || 0;
        signupCount += item.signups || 0;
        return {
          ...item,
          date: item.date || item.timestamp || new Date().toISOString()
        };
      }

      // Otherwise, determine the activity type from the data
      let isLogin = false;
      let isSignup = false;

      // Convert to strings to avoid type issues
      const typeStr = String(item.type || '').toLowerCase();
      const actionStr = String(item.action || '').toLowerCase();
      const messageStr = String(item.message || '').toLowerCase();
      const detailsStr = String(item.details || '').toLowerCase();

      // Check for signup events - more comprehensive matching
      if (
        typeStr === 'user_registration' ||
        (typeStr === 'auth' && (actionStr === 'signup' || actionStr === 'register')) ||
        actionStr === 'signup' ||
        actionStr === 'register' ||
        messageStr.includes('registered') ||
        messageStr.includes('signup') ||
        messageStr.includes('sign up') ||
        messageStr.includes('new user') ||
        detailsStr.includes('registered') ||
        detailsStr.includes('signup') ||
        detailsStr.includes('sign up') ||
        detailsStr.includes('new user')
      ) {
        isSignup = true;
        signupCount++;
      }

      // Check for login events - more comprehensive matching
      if (
        typeStr === 'user_login' ||
        (typeStr === 'auth' && (actionStr === 'login' || actionStr === 'signin')) ||
        actionStr === 'login' ||
        actionStr === 'signin' ||
        messageStr.includes('logged in') ||
        messageStr.includes('login') ||
        messageStr.includes('sign in') ||
        detailsStr.includes('logged in') ||
        detailsStr.includes('login') ||
        detailsStr.includes('sign in')
      ) {
        isLogin = true;
        loginCount++;
      }

      // Create a properly formatted activity item
      return {
        date: item.timestamp || item.date || new Date().toISOString(),
        logins: isLogin ? 1 : 0,
        signups: isSignup ? 1 : 0,
        type: item.type,
        action: item.action,
        message: item.message,
        details: item.details
      };
    });

    console.log(`Found ${loginCount} login events and ${signupCount} signup events`);

    return processedItems;
  }, [data]);

  // Count metrics from the processed data
  const loginCount = useMemo(() => {
    if (processedData && processedData.length > 0) {
      return processedData.reduce((sum, item) => sum + (item.logins || 0), 0);
    }
    return 0;
  }, [processedData]);

  const signupCount = useMemo(() => {
    if (processedData && processedData.length > 0) {
      return processedData.reduce((sum, item) => sum + (item.signups || 0), 0);
    }
    return 0;
  }, [processedData]);

  // We no longer track content views

  // Group activities by date for the chart
  const activityByDate = useMemo(() => {
    // Check if we have any real data
    if (!processedData || processedData.length === 0) {
      console.log('No processed activity data available');
      return [];
    }

    console.log('Processed data count:', processedData.length);
    console.log('Processed data sample:', processedData.slice(0, 3));

    // Get the last 7 days
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (6 - i));
      return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    });

    console.log('Last 7 days:', last7Days);

    // Initialize with zero counts for all days
    const dateMap = last7Days.reduce((acc, date) => {
      acc[date] = {
        date,
        logins: 0,
        signups: 0
      };
      return acc;
    }, {} as Record<string, { date: string; logins: number; signups: number }>);

    // Process real data
    console.log('Processing real data, count:', processedData.length);

    // Add actual data
    processedData.forEach(item => {
      if (!item.date && !item.timestamp) {
        console.log('Item missing date/timestamp:', item);
        return;
      }

      try {
        // Convert the timestamp to a date string (use date or timestamp field)
        const dateValue = item.date || item.timestamp;
        const itemDate = new Date(dateValue);

        if (isNaN(itemDate.getTime())) {
          console.log('Invalid date:', dateValue);
          return; // Skip invalid dates
        }

        const dateStr = itemDate.toISOString().split('T')[0];

        // Only count activities from the last 7 days
        if (dateMap[dateStr]) {
          // Simply add the login and signup counts from the processed item
          dateMap[dateStr].logins += item.logins || 0;
          dateMap[dateStr].signups += item.signups || 0;
        } else {
          // This date is outside our 7-day window
          // We don't need to log this as it's expected for some activities
        }
      } catch (error) {
        console.error('Error processing date:', item.date || item.timestamp, error);
      }
    });

    // Log the processed data for debugging
    console.log('Activity by date:', Object.values(dateMap));

    // Check if we have any actual data after processing
    const hasData = Object.values(dateMap).some(day => day.logins > 0 || day.signups > 0);

    // Log the data for each day
    Object.values(dateMap).forEach(day => {
      console.log(`Date ${day.date}: logins=${day.logins}, signups=${day.signups}`);
    });

    if (!hasData) {
      console.log('No activity data found in the last 7 days');
      return [];
    }

    return Object.values(dateMap);
  }, [processedData]);

  // Format dates for display
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  return (
    <CardContent className="p-6">
      <div className="space-y-4">
        {/* Activity chart visualization */}
        <div className="h-64 rounded-md">
          {activityByDate.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              <RechartsBarChart
                data={activityByDate}
                margin={{
                  top: 5,
                  right: 0,
                  left: -20, // Adjust to prevent Y-axis label cutoff
                  bottom: 5,
                }}
                barGap={4}
              >
                <defs>
                  <linearGradient id="loginGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1} />
                  </linearGradient>
                  <linearGradient id="signupGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#10b981" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="#10b981" stopOpacity={0.1} />
                  </linearGradient>
                </defs>
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="#374151"
                  opacity={0.3}
                  horizontal={true}
                  vertical={false}
                />
                <XAxis
                  dataKey="date"
                  tickFormatter={formatDate}
                  tick={{ fill: '#9CA3AF', fontSize: 11 }}
                  tickLine={false}
                  axisLine={{ stroke: "#4B5563", strokeWidth: 1 }}
                />
                <YAxis
                  tick={{ fill: '#9CA3AF', fontSize: 11 }}
                  tickLine={false}
                  axisLine={{ stroke: "#4B5563", strokeWidth: 1 }}
                  allowDecimals={false}
                />
                <Tooltip
                  cursor={{ fill: 'rgba(59, 130, 246, 0.1)', radius: 4 }}
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      return (
                        <div className="bg-vista-dark/95 backdrop-blur-sm border border-vista-light/20 rounded-lg p-3 shadow-xl">
                          <p className="text-vista-light font-medium mb-2">{formatDate(label)}</p>
                          {payload.map((entry: any, index: number) => (
                            <p key={index} className="text-sm flex items-center gap-2">
                              <span
                                className="w-3 h-3 rounded-sm"
                                style={{ backgroundColor: entry.color }}
                              />
                              {`${entry.dataKey}: ${entry.value}`}
                            </p>
                          ))}
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Legend
                  iconSize={12}
                  wrapperStyle={{
                    fontSize: '12px',
                    paddingTop: '15px',
                    color: '#9CA3AF'
                  }}
                />
                <Bar
                  dataKey="logins"
                  fill="url(#loginGradient)"
                  name="Logins"
                  radius={[3, 3, 0, 0]}
                  animationDuration={1200}
                  animationBegin={0}
                />
                <Bar
                  dataKey="signups"
                  fill="url(#signupGradient)"
                  name="Signups"
                  radius={[3, 3, 0, 0]}
                  animationDuration={1200}
                  animationBegin={200}
                />
              </RechartsBarChart>
            </ResponsiveContainer>
          ) : (
            <div className="h-full flex items-center justify-center bg-slate-800 rounded-md">
              <div className="text-center">
                <RechartsBarChart className="h-12 w-12 mx-auto text-blue-400 opacity-70 mb-2" />
                <p className="text-slate-300">No user activity data available for the last 7 days</p>
                <p className="text-slate-400 text-sm mt-1">User logins and signups will appear here when they occur</p>
              </div>
            </div>
          )}
        </div>

        {/* Enhanced Summary stats */}
        <div className="grid grid-cols-2 gap-4 mt-6">
          <div className="bg-gradient-to-br from-blue-500/10 to-blue-600/5 border border-blue-500/20 p-4 rounded-lg backdrop-blur-sm">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <div className="p-2 bg-blue-500/20 rounded-lg mr-3">
                  <Users className="h-4 w-4 text-blue-400" />
                </div>
                <p className="text-sm text-vista-light/80 font-medium">Logins</p>
              </div>
              <div className="text-xs text-blue-400 bg-blue-500/10 px-2 py-1 rounded-full">
                7 days
              </div>
            </div>
            {loginCount > 0 ? (
              <>
                <p className="text-2xl font-bold text-white mb-1">{loginCount.toLocaleString()}</p>
                <p className="text-xs text-blue-400">
                  User sessions
                </p>
              </>
            ) : (
              <div className="py-2">
                <p className="text-lg text-vista-light/50 mb-1">0</p>
                <p className="text-xs text-vista-light/40">No recent logins</p>
              </div>
            )}
          </div>
          <div className="bg-gradient-to-br from-emerald-500/10 to-emerald-600/5 border border-emerald-500/20 p-4 rounded-lg backdrop-blur-sm">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <div className="p-2 bg-emerald-500/20 rounded-lg mr-3">
                  <Calendar className="h-4 w-4 text-emerald-400" />
                </div>
                <p className="text-sm text-vista-light/80 font-medium">Signups</p>
              </div>
              <div className="text-xs text-emerald-400 bg-emerald-500/10 px-2 py-1 rounded-full">
                7 days
              </div>
            </div>
            {signupCount > 0 ? (
              <>
                <p className="text-2xl font-bold text-white mb-1">{signupCount.toLocaleString()}</p>
                <p className="text-xs text-emerald-400">
                  New users joined
                </p>
              </>
            ) : (
              <div className="py-2">
                <p className="text-lg text-vista-light/50 mb-1">0</p>
                <p className="text-xs text-vista-light/40">No new signups</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </CardContent>
  );
}
