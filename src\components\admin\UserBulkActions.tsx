'use client';

import { useState } from 'react';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { 
  CheckCircle, 
  ChevronDown, 
  Loader2, 
  Shield, 
  Trash2, 
  UserCheck, 
  UserX 
} from 'lucide-react';

interface UserBulkActionsProps {
  selectedUsers: string[];
  onActionComplete: () => void;
}

export default function UserBulkActions({ 
  selectedUsers, 
  onActionComplete 
}: UserBulkActionsProps) {
  const [isLoading, setIsLoading] = useState(false);
  const hasSelection = selectedUsers.length > 0;

  // Handle bulk role change
  const handleBulkRoleChange = async (newRole: string) => {
    if (!hasSelection) return;
    
    if (!confirm(`Are you sure you want to change ${selectedUsers.length} user(s) to ${newRole} role?`)) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Make API request to update roles
      const response = await fetch('/api/admin/users/bulk', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userIds: selectedUsers,
          action: 'changeRole',
          role: newRole
        }),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update user roles');
      }
      
      toast({
        title: 'Roles Updated',
        description: `Successfully updated ${selectedUsers.length} user(s) to ${newRole} role.`,
        variant: 'success'
      });
      
      onActionComplete();
    } catch (error) {
      console.error('Error updating roles:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle bulk verification status change
  const handleBulkVerificationChange = async (verified: boolean) => {
    if (!hasSelection) return;
    
    const action = verified ? 'verify' : 'unverify';
    if (!confirm(`Are you sure you want to ${action} ${selectedUsers.length} user(s)?`)) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Make API request to update verification status
      const response = await fetch('/api/admin/users/bulk', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userIds: selectedUsers,
          action: 'changeVerification',
          verified
        }),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update verification status');
      }
      
      toast({
        title: 'Verification Status Updated',
        description: `Successfully ${verified ? 'verified' : 'unverified'} ${selectedUsers.length} user(s).`,
        variant: 'success'
      });
      
      onActionComplete();
    } catch (error) {
      console.error('Error updating verification status:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle bulk deletion
  const handleBulkDelete = async () => {
    if (!hasSelection) return;
    
    if (!confirm(`Are you sure you want to delete ${selectedUsers.length} user(s)? This action cannot be undone.`)) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Make API request to delete users
      const response = await fetch('/api/admin/users/bulk', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userIds: selectedUsers
        }),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete users');
      }
      
      toast({
        title: 'Users Deleted',
        description: `Successfully deleted ${selectedUsers.length} user(s).`,
        variant: 'success'
      });
      
      onActionComplete();
    } catch (error) {
      console.error('Error deleting users:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          disabled={!hasSelection || isLoading}
          className="flex items-center"
        >
          {isLoading ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <CheckCircle className="mr-2 h-4 w-4" />
          )}
          Bulk Actions
          <ChevronDown className="ml-2 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>
          {selectedUsers.length} user{selectedUsers.length !== 1 ? 's' : ''} selected
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuLabel>Change Role</DropdownMenuLabel>
        <DropdownMenuItem onClick={() => handleBulkRoleChange('user')}>
          <UserCheck className="mr-2 h-4 w-4" />
          Set as User
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleBulkRoleChange('moderator')}>
          <Shield className="mr-2 h-4 w-4" />
          Set as Moderator
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleBulkRoleChange('admin')}>
          <Shield className="mr-2 h-4 w-4" />
          Set as Admin
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuLabel>Verification</DropdownMenuLabel>
        <DropdownMenuItem onClick={() => handleBulkVerificationChange(true)}>
          <CheckCircle className="mr-2 h-4 w-4" />
          Mark as Verified
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleBulkVerificationChange(false)}>
          <UserX className="mr-2 h-4 w-4" />
          Mark as Unverified
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          onClick={handleBulkDelete}
          className="text-red-500 focus:text-red-500"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Delete Selected
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
