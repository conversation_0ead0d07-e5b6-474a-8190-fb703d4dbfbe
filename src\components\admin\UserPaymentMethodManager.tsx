'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from '@/components/ui/use-toast';
import { Loader2, CreditCard, Trash2, Plus, RefreshCw } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface PaymentMethod {
  id: string;
  type: string;
  isDefault: boolean;
  lastFour?: string;
  expiryMonth?: number;
  expiryYear?: number;
  cardBrand?: string;
  cardholderName?: string;
  paypalEmail?: string;
  bankName?: string;
  createdAt: Date;
}

interface UserPaymentMethodManagerProps {
  userId: string;
  onSuccess?: () => void;
}

export default function UserPaymentMethodManager({ userId, onSuccess }: UserPaymentMethodManagerProps) {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  
  // Form state
  const [formData, setFormData] = useState({
    type: 'credit_card',
    isDefault: false,
    lastFour: '',
    expiryMonth: '',
    expiryYear: '',
    cardBrand: '',
    cardholderName: '',
    paypalEmail: '',
    bankName: '',
    bankAccountLast4: ''
  });

  // Fetch payment methods on mount
  useEffect(() => {
    fetchPaymentMethods();
  }, [userId]);

  // Fetch payment methods from API
  const fetchPaymentMethods = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/admin/users/${userId}/payment-methods`, {
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to fetch payment methods (${response.status})`);
      }

      const data = await response.json();
      setPaymentMethods(data || []);
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle select change
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle checkbox change
  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      const response = await fetch(`/api/admin/users/${userId}/payment-methods`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData),
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to add payment method (${response.status})`);
      }
      
      const data = await response.json();
      
      toast({
        title: 'Payment Method Added',
        description: 'Successfully added new payment method.',
        variant: 'success'
      });
      
      // Close dialog
      setIsAddDialogOpen(false);
      
      // Reset form
      setFormData({
        type: 'credit_card',
        isDefault: false,
        lastFour: '',
        expiryMonth: '',
        expiryYear: '',
        cardBrand: '',
        cardholderName: '',
        paypalEmail: '',
        bankName: '',
        bankAccountLast4: ''
      });
      
      // Refresh payment methods
      fetchPaymentMethods();
      
      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error adding payment method:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle payment method deletion
  const handleDelete = async (paymentMethodId: string) => {
    if (!confirm('Are you sure you want to delete this payment method?')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/admin/users/${userId}/payment-methods?paymentMethodId=${paymentMethodId}`, {
        method: 'DELETE',
        credentials: 'include'
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to delete payment method (${response.status})`);
      }
      
      toast({
        title: 'Payment Method Deleted',
        description: 'Successfully deleted payment method.',
        variant: 'success'
      });
      
      // Refresh payment methods
      fetchPaymentMethods();
      
      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error deleting payment method:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive'
      });
    }
  };

  // Get card icon based on brand
  const getCardIcon = (brand?: string) => {
    // In a real implementation, you would use different icons for different card brands
    return <CreditCard className="h-5 w-5" />;
  };

  // Format expiry date
  const formatExpiry = (month?: number, year?: number) => {
    if (!month || !year) return 'N/A';
    return `${month.toString().padStart(2, '0')}/${year.toString().slice(-2)}`;
  };

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-8 flex justify-center items-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-vista-blue" />
            <p className="text-vista-light">Loading payment methods...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-vista-light">Payment Methods</CardTitle>
          <CardDescription>
            Manage user payment methods
          </CardDescription>
        </CardHeader>
        <CardContent className="p-8 text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <Button onClick={fetchPaymentMethods}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
          <div>
            <CardTitle className="text-vista-light flex items-center gap-2">
              <CreditCard className="h-5 w-5 text-vista-blue" />
              Payment Methods
            </CardTitle>
            <CardDescription>
              Manage user payment methods
            </CardDescription>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Add Payment Method
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Payment Method</DialogTitle>
                <DialogDescription>
                  Add a new payment method for this user.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="type">Payment Method Type</Label>
                  <Select 
                    value={formData.type} 
                    onValueChange={(value) => handleSelectChange('type', value)}
                  >
                    <SelectTrigger id="type">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="credit_card">Credit Card</SelectItem>
                      <SelectItem value="paypal">PayPal</SelectItem>
                      <SelectItem value="bank_account">Bank Account</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                {formData.type === 'credit_card' && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="cardBrand">Card Brand</Label>
                      <Select 
                        value={formData.cardBrand} 
                        onValueChange={(value) => handleSelectChange('cardBrand', value)}
                      >
                        <SelectTrigger id="cardBrand">
                          <SelectValue placeholder="Select card brand" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="visa">Visa</SelectItem>
                          <SelectItem value="mastercard">Mastercard</SelectItem>
                          <SelectItem value="amex">American Express</SelectItem>
                          <SelectItem value="discover">Discover</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastFour">Last 4 Digits</Label>
                      <Input
                        id="lastFour"
                        name="lastFour"
                        value={formData.lastFour}
                        onChange={handleInputChange}
                        maxLength={4}
                        placeholder="1234"
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="expiryMonth">Expiry Month</Label>
                        <Input
                          id="expiryMonth"
                          name="expiryMonth"
                          value={formData.expiryMonth}
                          onChange={handleInputChange}
                          placeholder="MM"
                          maxLength={2}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="expiryYear">Expiry Year</Label>
                        <Input
                          id="expiryYear"
                          name="expiryYear"
                          value={formData.expiryYear}
                          onChange={handleInputChange}
                          placeholder="YYYY"
                          maxLength={4}
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="cardholderName">Cardholder Name</Label>
                      <Input
                        id="cardholderName"
                        name="cardholderName"
                        value={formData.cardholderName}
                        onChange={handleInputChange}
                        placeholder="John Doe"
                      />
                    </div>
                  </>
                )}
                
                {formData.type === 'paypal' && (
                  <div className="space-y-2">
                    <Label htmlFor="paypalEmail">PayPal Email</Label>
                    <Input
                      id="paypalEmail"
                      name="paypalEmail"
                      type="email"
                      value={formData.paypalEmail}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                    />
                  </div>
                )}
                
                {formData.type === 'bank_account' && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="bankName">Bank Name</Label>
                      <Input
                        id="bankName"
                        name="bankName"
                        value={formData.bankName}
                        onChange={handleInputChange}
                        placeholder="Bank of America"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="bankAccountLast4">Last 4 Digits of Account</Label>
                      <Input
                        id="bankAccountLast4"
                        name="bankAccountLast4"
                        value={formData.bankAccountLast4}
                        onChange={handleInputChange}
                        maxLength={4}
                        placeholder="1234"
                      />
                    </div>
                  </>
                )}
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isDefault"
                    checked={formData.isDefault}
                    onCheckedChange={(checked) => 
                      handleCheckboxChange('isDefault', checked === true)
                    }
                  />
                  <Label htmlFor="isDefault">Set as default payment method</Label>
                </div>
                
                <DialogFooter>
                  <DialogClose asChild>
                    <Button type="button" variant="outline">
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Plus className="mr-2 h-4 w-4" />
                    )}
                    Add Payment Method
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {paymentMethods.length === 0 ? (
          <div className="text-center py-8 text-vista-light/70">
            <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-20" />
            <p>No payment methods found.</p>
            <Button 
              variant="outline" 
              size="sm" 
              className="mt-4"
              onClick={() => setIsAddDialogOpen(true)}
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Payment Method
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {paymentMethods.map((method) => (
              <div 
                key={method.id} 
                className="flex items-center justify-between p-4 rounded-md border border-vista-light/10 bg-vista-dark"
              >
                <div className="flex items-center gap-4">
                  <div className="bg-vista-light/5 p-2 rounded-md">
                    {getCardIcon(method.cardBrand)}
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <p className="font-medium text-vista-light capitalize">
                        {method.type.replace('_', ' ')}
                        {method.lastFour && ` •••• ${method.lastFour}`}
                      </p>
                      {method.isDefault && (
                        <Badge variant="outline" className="text-xs">Default</Badge>
                      )}
                    </div>
                    <p className="text-sm text-vista-light/70">
                      {method.type === 'credit_card' && (
                        <>
                          {method.cardBrand && (
                            <span className="capitalize">{method.cardBrand}</span>
                          )}
                          {method.expiryMonth && method.expiryYear && (
                            <span> • Expires {formatExpiry(method.expiryMonth, method.expiryYear)}</span>
                          )}
                        </>
                      )}
                      {method.type === 'paypal' && method.paypalEmail && (
                        <span>{method.paypalEmail}</span>
                      )}
                      {method.type === 'bank_account' && method.bankName && (
                        <span>{method.bankName}</span>
                      )}
                    </p>
                  </div>
                </div>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => handleDelete(method.id)}
                >
                  <Trash2 className="h-4 w-4 text-red-500" />
                  <span className="sr-only">Delete</span>
                </Button>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between border-t border-vista-light/10 pt-6">
        <div className="text-sm text-vista-light/70">
          <p>{paymentMethods.length} payment method(s)</p>
        </div>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={fetchPaymentMethods}
          disabled={isLoading}
        >
          <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </CardFooter>
    </Card>
  );
}
