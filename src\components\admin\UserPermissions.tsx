'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Save, Shield, RefreshCw } from 'lucide-react';

interface Permission {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
}

interface PermissionGroup {
  id: string;
  name: string;
  permissions: Permission[];
}

interface UserPermissionsProps {
  userId: string;
}

export default function UserPermissions({ userId }: UserPermissionsProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDefault, setIsDefault] = useState(false);
  const [userRole, setUserRole] = useState<string>('user');

  // Permission groups state
  const [permissionGroups, setPermissionGroups] = useState<PermissionGroup[]>([
    {
      id: 'content',
      name: 'Content Access',
      permissions: [
        {
          id: 'content.view',
          name: 'View Content',
          description: 'Can view all content on the platform',
          enabled: true
        },
        {
          id: 'content.premium',
          name: 'Premium Content',
          description: 'Can access premium content',
          enabled: false
        },
        {
          id: 'content.early_access',
          name: 'Early Access',
          description: 'Can access content before official release',
          enabled: false
        }
      ]
    },
    {
      id: 'social',
      name: 'Social Features',
      permissions: [
        {
          id: 'social.comment',
          name: 'Comments',
          description: 'Can comment on content',
          enabled: true
        },
        {
          id: 'social.rate',
          name: 'Ratings',
          description: 'Can rate content',
          enabled: true
        },
        {
          id: 'social.share',
          name: 'Sharing',
          description: 'Can share content on social media',
          enabled: true
        }
      ]
    },
    {
      id: 'account',
      name: 'Account Management',
      permissions: [
        {
          id: 'account.profile',
          name: 'Edit Profile',
          description: 'Can edit their profile information',
          enabled: true
        },
        {
          id: 'account.subscription',
          name: 'Manage Subscription',
          description: 'Can manage their subscription',
          enabled: true
        },
        {
          id: 'account.payment',
          name: 'Payment Methods',
          description: 'Can add/edit payment methods',
          enabled: true
        }
      ]
    }
  ]);

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch user permissions
  useEffect(() => {
    fetchUserPermissions();
  }, [userId]);

  // Fetch user permissions from API
  const fetchUserPermissions = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Get the admin userId from localStorage
      const adminUserId = localStorage.getItem('userId');

      if (!adminUserId) {
        throw new Error('Authentication required. Please sign in again.');
      }

      const response = await fetch(`/api/admin/users/${userId}/permissions?userId=${adminUserId}`, {
        credentials: 'include',
        headers: {
          'Authorization': `Bearer ${adminUserId}`,
          'Cache-Control': 'no-cache'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to fetch permissions (${response.status})`);
      }

      const data = await response.json();

      // Set user role
      setUserRole(data.user.role || 'user');

      // Set default flag
      setIsDefault(data.isDefault);

      // Transform API permissions to component state
      const groups: PermissionGroup[] = [
        {
          id: 'content',
          name: 'Content Access',
          permissions: [
            {
              id: 'content.view',
              name: 'View Content',
              description: 'Can view all content on the platform',
              enabled: !!data.permissions['content.view']
            },
            {
              id: 'content.premium',
              name: 'Premium Content',
              description: 'Can access premium content',
              enabled: !!data.permissions['content.premium']
            },
            {
              id: 'content.early_access',
              name: 'Early Access',
              description: 'Can access content before official release',
              enabled: !!data.permissions['content.early_access']
            }
          ]
        },
        {
          id: 'social',
          name: 'Social Features',
          permissions: [
            {
              id: 'social.comment',
              name: 'Comments',
              description: 'Can comment on content',
              enabled: !!data.permissions['social.comment']
            },
            {
              id: 'social.rate',
              name: 'Ratings',
              description: 'Can rate content',
              enabled: !!data.permissions['social.rate']
            },
            {
              id: 'social.share',
              name: 'Sharing',
              description: 'Can share content on social media',
              enabled: !!data.permissions['social.share']
            }
          ]
        },
        {
          id: 'account',
          name: 'Account Management',
          permissions: [
            {
              id: 'account.profile',
              name: 'Edit Profile',
              description: 'Can edit their profile information',
              enabled: !!data.permissions['account.profile']
            },
            {
              id: 'account.subscription',
              name: 'Manage Subscription',
              description: 'Can manage their subscription',
              enabled: !!data.permissions['account.subscription']
            },
            {
              id: 'account.payment',
              name: 'Payment Methods',
              description: 'Can add/edit payment methods',
              enabled: !!data.permissions['account.payment']
            }
          ]
        }
      ];

      // Add moderator permissions if applicable
      if (data.permissions['moderator.review_comments'] !== undefined ||
          data.permissions['moderator.approve_content'] !== undefined) {
        groups.push({
          id: 'moderator',
          name: 'Moderator Capabilities',
          permissions: [
            {
              id: 'moderator.review_comments',
              name: 'Review Comments',
              description: 'Can review and moderate user comments',
              enabled: !!data.permissions['moderator.review_comments']
            },
            {
              id: 'moderator.approve_content',
              name: 'Approve Content',
              description: 'Can approve user-submitted content',
              enabled: !!data.permissions['moderator.approve_content']
            }
          ]
        });
      }

      // Add admin permissions if applicable
      if (data.permissions['admin.manage_users'] !== undefined ||
          data.permissions['admin.manage_content'] !== undefined ||
          data.permissions['admin.manage_settings'] !== undefined ||
          data.permissions['admin.view_analytics'] !== undefined) {
        groups.push({
          id: 'admin',
          name: 'Admin Capabilities',
          permissions: [
            {
              id: 'admin.manage_users',
              name: 'Manage Users',
              description: 'Can manage user accounts',
              enabled: !!data.permissions['admin.manage_users']
            },
            {
              id: 'admin.manage_content',
              name: 'Manage Content',
              description: 'Can manage platform content',
              enabled: !!data.permissions['admin.manage_content']
            },
            {
              id: 'admin.manage_settings',
              name: 'Manage Settings',
              description: 'Can manage platform settings',
              enabled: !!data.permissions['admin.manage_settings']
            },
            {
              id: 'admin.view_analytics',
              name: 'View Analytics',
              description: 'Can view platform analytics',
              enabled: !!data.permissions['admin.view_analytics']
            }
          ]
        });
      }

      setPermissionGroups(groups);
    } catch (error) {
      console.error('Error fetching user permissions:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Toggle a permission
  const togglePermission = (groupId: string, permissionId: string) => {
    setPermissionGroups(prevGroups =>
      prevGroups.map(group => {
        if (group.id === groupId) {
          return {
            ...group,
            permissions: group.permissions.map(permission => {
              if (permission.id === permissionId) {
                return {
                  ...permission,
                  enabled: !permission.enabled
                };
              }
              return permission;
            })
          };
        }
        return group;
      })
    );
  };

  // Toggle all permissions in a group
  const toggleGroupPermissions = (groupId: string, enabled: boolean) => {
    setPermissionGroups(prevGroups =>
      prevGroups.map(group => {
        if (group.id === groupId) {
          return {
            ...group,
            permissions: group.permissions.map(permission => ({
              ...permission,
              enabled
            }))
          };
        }
        return group;
      })
    );
  };

  // Reset permissions to default for user role
  const resetPermissions = async () => {
    if (!confirm(`Are you sure you want to reset permissions to ${userRole} defaults?`)) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Get the admin userId from localStorage
      const adminUserId = localStorage.getItem('userId');

      if (!adminUserId) {
        throw new Error('Authentication required. Please sign in again.');
      }

      const response = await fetch(`/api/admin/users/${userId}/permissions?userId=${adminUserId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${adminUserId}`,
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({ reset: true }),
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to reset permissions (${response.status})`);
      }

      toast({
        title: 'Permissions Reset',
        description: `User permissions have been reset to ${userRole} defaults.`,
        variant: 'success'
      });

      // Refresh permissions
      await fetchUserPermissions();
    } catch (error) {
      console.error('Error resetting permissions:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to reset permissions',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Save permissions
  const savePermissions = async () => {
    setIsSubmitting(true);

    try {
      // Format permissions for API
      const permissionsObj: Record<string, boolean> = {};

      permissionGroups.forEach(group => {
        group.permissions.forEach(permission => {
          permissionsObj[permission.id] = permission.enabled;
        });
      });

      // Get the admin userId from localStorage
      const adminUserId = localStorage.getItem('userId');

      if (!adminUserId) {
        throw new Error('Authentication required. Please sign in again.');
      }

      // Make API call
      const response = await fetch(`/api/admin/users/${userId}/permissions?userId=${adminUserId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${adminUserId}`,
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({ permissions: permissionsObj }),
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update permissions (${response.status})`);
      }

      // Update isDefault flag
      setIsDefault(false);

      toast({
        title: 'Permissions Updated',
        description: 'User permissions have been updated successfully.',
        variant: 'success'
      });
    } catch (error) {
      console.error('Error saving permissions:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update permissions',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Check if all permissions in a group are enabled
  const areAllPermissionsEnabled = (groupId: string) => {
    const group = permissionGroups.find(g => g.id === groupId);
    return group ? group.permissions.every(p => p.enabled) : false;
  };

  // Check if any permissions in a group are enabled
  const areAnyPermissionsEnabled = (groupId: string) => {
    const group = permissionGroups.find(g => g.id === groupId);
    return group ? group.permissions.some(p => p.enabled) : false;
  };

  return (
    <Card className="relative">
      {isLoading && (
        <div className="absolute inset-0 bg-vista-dark/50 flex items-center justify-center z-10 rounded-md">
          <Loader2 className="h-8 w-8 animate-spin text-vista-blue" />
        </div>
      )}

      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
          <div>
            <CardTitle className="text-vista-light flex items-center gap-2">
              <Shield className="h-5 w-5 text-vista-blue" />
              User Permissions
            </CardTitle>
            <CardDescription>
              Manage what this user can do on the platform
            </CardDescription>
          </div>

          {isDefault && (
            <div className="bg-vista-blue/10 text-vista-blue text-sm py-1 px-3 rounded-full">
              Using {userRole} defaults
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {error && (
          <div className="bg-red-500/10 border border-red-500/20 text-red-500 p-4 rounded-md">
            <p className="font-medium">Error loading permissions</p>
            <p className="text-sm">{error}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchUserPermissions}
              className="mt-2"
            >
              <RefreshCw className="mr-2 h-3 w-3" />
              Try Again
            </Button>
          </div>
        )}
        {permissionGroups.map(group => (
          <div key={group.id} className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-vista-light">{group.name}</h3>
              <div className="flex items-center gap-2">
                <Label htmlFor={`group-${group.id}`} className="text-sm text-vista-light/70">
                  {areAllPermissionsEnabled(group.id)
                    ? 'All Enabled'
                    : areAnyPermissionsEnabled(group.id)
                      ? 'Partially Enabled'
                      : 'All Disabled'}
                </Label>
                <Switch
                  id={`group-${group.id}`}
                  checked={areAllPermissionsEnabled(group.id)}
                  onCheckedChange={(checked) => toggleGroupPermissions(group.id, checked)}
                />
              </div>
            </div>

            <div className="space-y-2">
              {group.permissions.map(permission => (
                <div
                  key={permission.id}
                  className="flex items-center justify-between p-2 rounded-md bg-vista-dark"
                >
                  <div>
                    <p className="font-medium text-vista-light">{permission.name}</p>
                    <p className="text-vista-light/70 text-sm">{permission.description}</p>
                  </div>
                  <Switch
                    id={permission.id}
                    checked={permission.enabled}
                    onCheckedChange={() => togglePermission(group.id, permission.id)}
                  />
                </div>
              ))}
            </div>

            {group !== permissionGroups[permissionGroups.length - 1] && (
              <Separator className="my-4" />
            )}
          </div>
        ))}
      </CardContent>
      <CardFooter className="border-t border-vista-light/10 pt-4 flex justify-between">
        <Button
          variant="outline"
          onClick={resetPermissions}
          disabled={isSubmitting}
        >
          Reset to {userRole} Defaults
        </Button>

        <Button
          onClick={savePermissions}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Save className="mr-2 h-4 w-4" />
          )}
          Save Permissions
        </Button>
      </CardFooter>
    </Card>
  );
}
