'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { handleGoogleSignIn } from '@/lib/auth';
import { UserSession } from '@/lib/types';
import { useRouter } from 'next/navigation';

// Define the context shape
interface AuthContextType {
  user: UserSession | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signUp: (name: string, email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<{ success: boolean; error?: string }>; // Updated to return Promise
  googleSignIn: (googleData: GoogleSignInResponse) => Promise<{ success: boolean; error?: string }>;
  networkError: boolean;
  refreshAuthState: () => Promise<void>;
  updateProfileImage: (imageUrl: string) => Promise<{ success: boolean; error?: string }>;
  updateUserName: (name: string) => Promise<{ success: boolean; error?: string }>;
  isAdmin: () => boolean;
  verifyAdminStatus: () => Promise<boolean>;
}

// Define types for Google Sign-In Response
interface GoogleSignInResponse {
  credential?: string;
  [key: string]: unknown;
}

// Define a type for profile objects with isPrimary property
interface ProfileWithIsPrimary {
  id: string;
  isPrimary: boolean;
  [key: string]: unknown;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  signIn: async () => ({ success: false }),
  signUp: async () => ({ success: false }),
  signOut: async () => ({ success: false }), // Updated to return Promise
  googleSignIn: async () => ({ success: false }),
  networkError: false,
  refreshAuthState: async () => {},
  updateProfileImage: async () => ({ success: false }),
  updateUserName: async () => ({ success: false }),
  isAdmin: () => false,
  verifyAdminStatus: async () => false,
});

// Hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Provider component that wraps the app and makes auth available
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<UserSession | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [networkError, setNetworkError] = useState(false);
  const router = useRouter();

  // Track last auth check time to prevent excessive calls
  const lastAuthCheckRef = React.useRef<number>(0);
  const AUTH_CHECK_INTERVAL = 60000; // 60 seconds between checks

  // Define a simple function to clear user data for use in loadUserAndVerify
  const clearUserData = useCallback(() => {
    setUser(null);
    localStorage.removeItem('user');
    localStorage.removeItem('userId');
    localStorage.removeItem('watchPartyUserId');
  }, [setUser]);

  // Function to load and verify user
  const loadUserAndVerify = useCallback(async () => {
    setIsLoading(true);

    try {
      // First check local storage for existing session
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        try {
          const parsedUser = JSON.parse(storedUser);

          // Check if the parsed user has an id
          if (!parsedUser || !parsedUser.id) {
            console.log('Invalid user data in localStorage, clearing session');
            clearUserData(); // Use clearUserData instead of handleSignOut
            setIsLoading(false);
            return;
          }

          setUser(parsedUser); // Set user immediately from local storage

          // Ensure userId is also stored separately for components that need it
          if (parsedUser.id && !localStorage.getItem('userId')) {
            localStorage.setItem('userId', parsedUser.id);
          }

          // Then verify with server with retry logic for better reliability
          try {
            // Add retry logic for better reliability during page refreshes
            // MOBILE OPTIMIZATION: Reduced retry count and timeout for faster loading
            const verifySession = async (retryCount = 0, maxRetries = 1) => {
              try {
                console.log(`Verifying session for user: ${parsedUser.id}`);
                
                // Mobile optimization: Shorter timeout for faster mobile experience
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout instead of default
                
                const response = await fetch('/api/auth/session', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ userId: parsedUser.id }),
                  credentials: 'include', // Ensure cookies are sent
                  signal: controller.signal
                });

                clearTimeout(timeoutId);
                const data = await response.json();

                if (response.ok) {
                  if (data.valid) {
                    // Session is valid - update user with fresh data if available
                    if (data.user && !data.partialValidation) {
                      setUser(data.user);
                      localStorage.setItem('user', JSON.stringify(data.user));
                      setNetworkError(false);

                      // We no longer update lastLogin during session verification
                      // lastLogin should only be updated during actual login events
                    } else if (data.partialValidation) {
                      // Connection issue but session is still considered valid
                      // Keep existing user data and set network error flag
                      console.log('Using cached user data due to database connectivity issues');
                      setNetworkError(true);
                    }
                  } else {
                    // Server says session is invalid
                    console.log('Session invalid, signing out');
                    clearUserData(); // Use clearUserData instead of handleSignOut
                    setNetworkError(false);
                  }
                } else {
                  // Handle retry for network errors or temporary issues
                  if (retryCount < maxRetries && (response.status >= 500 || response.status === 0)) {
                    console.log(`Retrying session validation (${retryCount + 1}/${maxRetries})...`);
                    // Reduced backoff for mobile
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    return verifySession(retryCount + 1, maxRetries);
                  }

                  // Network error or server error
                  console.error('Session verification failed:', data.error || 'No error message provided');

                  // If the user is not found, sign out
                  if (data.error === 'User not found' || response.status === 404) {
                    console.log('User not found, signing out');
                    clearUserData(); // Use clearUserData instead of handleSignOut
                    setNetworkError(false);
                  } else if (data.error && (data.error.includes('Critical error') || data.error.includes('Database'))) {
                    // Handle critical errors and database errors by maintaining session but showing warning
                    console.warn('Critical or database error during session validation - using cached credentials');
                    console.warn('Error details:', data.error);
                    console.warn('Error response status:', response.status);

                    // Log additional details for debugging
                    if (data.details) {
                      console.warn('Error details from server:', data.details);
                    }

                    // Set network error flag but maintain the session
                    setNetworkError(true);

                    // Don't clear user data for critical errors - maintain the session
                    // This prevents users from being logged out due to temporary database issues
                  } else {
                    // For other errors, don't sign out - maintain session
                    // Just set the network error flag to show appropriate UI
                    console.log('Maintaining session despite verification error:', data.error);
                    setNetworkError(true);
                  }
                }
              } catch (fetchError) {
                // Handle abort errors (timeout) gracefully
                if (fetchError instanceof Error && fetchError.name === 'AbortError') {
                  console.log('Session verification timed out - maintaining cached session for better mobile UX');
                  setNetworkError(true);
                  return;
                }

                // Network/fetch error - don't sign out for transient errors
                console.error('Error verifying session:', fetchError);

                if (fetchError instanceof Error) {
                  console.error('Error name:', fetchError.name);
                  console.error('Error message:', fetchError.message);

                  // Check for specific error types that might indicate a more serious issue
                  const errorMessage = fetchError.message.toLowerCase();

                  // If it's a SyntaxError, it might be a malformed response from the server
                  if (fetchError instanceof SyntaxError) {
                    console.warn('Received malformed JSON response from server during session validation');
                  }

                  // If it contains specific network-related terms, it's likely a connectivity issue
                  if (errorMessage.includes('network') ||
                      errorMessage.includes('failed to fetch') ||
                      errorMessage.includes('connection') ||
                      errorMessage.includes('timeout')) {
                    console.warn('Network connectivity issue detected during session validation');
                  }
                }

                // Retry for network errors - reduced retry for mobile
                if (retryCount < maxRetries) {
                  console.log(`Retrying after fetch error (${retryCount + 1}/${maxRetries})...`);
                  // Reduced backoff for mobile
                  await new Promise(resolve => setTimeout(resolve, 2000));
                  return verifySession(retryCount + 1, maxRetries);
                }

                // For network errors, maintain session but set network error flag
                console.log('Network error during session validation - maintaining user session with cached credentials');
                setNetworkError(true);
              }
            };

            await verifySession();
          } catch (fetchError) {
            // Network/fetch error - don't sign out
            console.error('Error in verifySession function:', fetchError);

            if (fetchError instanceof Error) {
              console.error('Error name:', fetchError.name);
              console.error('Error message:', fetchError.message);
              console.error('Error stack:', fetchError.stack);
            }

            // For outer errors, maintain session but set network error flag
            console.log('Outer network error - maintaining user session with cached credentials');
            setNetworkError(true);
          }
        } catch (parseError) {
          // Error parsing stored user JSON
          console.error('Error parsing stored user data:', parseError);
          clearUserData(); // Use clearUserData instead of handleSignOut
        }
      } else {
        // No stored user - this is a normal state for new visitors
        console.log('No stored user session found');
        setUser(null);
      }
    } catch (error) {
      console.error('Error loading user from storage:', error);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  }, [setIsLoading, setUser, setNetworkError, clearUserData]);

  // Implement refreshAuthState function with throttling
  const refreshAuthState = useCallback(async (force: boolean = false) => {
    const now = Date.now();
    const timeSinceLastCheck = now - lastAuthCheckRef.current;

    // Skip if checking too frequently and not forced
    if (!force && timeSinceLastCheck < AUTH_CHECK_INTERVAL) {
      return;
    }

    // Update last check time
    lastAuthCheckRef.current = now;

    if (process.env.NODE_ENV === 'development') {
      console.log('Refreshing auth state...');
    }

    await loadUserAndVerify();
  }, [loadUserAndVerify]);

  // Load user from local storage on initial load
  // Then verify the session with the server
  useEffect(() => {
    // Initialize immediately without delay to prevent loading issues
    loadUserAndVerify();

    // Set up a periodic session validation for long sessions
    const sessionCheckInterval = setInterval(() => {
      if (user?.id) {
        if (process.env.NODE_ENV === 'development') {
          console.log('Performing periodic session validation');
        }
        refreshAuthState(false);
      }
    }, 30 * 60 * 1000); // Check every 30 minutes

    // Listen for storage events from other tabs
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'user') {
        if (process.env.NODE_ENV === 'development') {
          console.log('User storage changed in another tab');
        }
        refreshAuthState(true); // Force refresh on storage events
      }

      // Listen for profile image updates from other tabs
      if (e.key === 'lastProfileImageUpdate' && e.newValue) {
        try {
          const updateData = JSON.parse(e.newValue);
          if (updateData.userId === user?.id) {
            console.log('Profile image updated in another tab, updating local state');

            // Update the user state with the new image URL
            setUser(prevUser => {
              if (!prevUser) return null;

              const updatedUser = {
                ...prevUser,
                picture: updateData.imageUrl,
                profileImage: updateData.imageUrl
              };

              return updatedUser;
            });
          }
        } catch (error) {
          console.error('Error processing profile image update from another tab:', error);
        }
      }
    };

    // Listen for profile image change events from this tab
    const handleProfileImageChange = (e: Event) => {
      const customEvent = e as CustomEvent;
      if (customEvent.detail && customEvent.detail.userId !== user?.id) {
        console.log('Profile image changed for another user, refreshing if needed');
        refreshAuthState(false);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('profile-image-changed', handleProfileImageChange);

    return () => {
      clearInterval(sessionCheckInterval);
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('profile-image-changed', handleProfileImageChange);
    };
  }, [loadUserAndVerify, refreshAuthState, user?.id]);

  // Sign in function
  const signIn = async (email: string, password: string) => {
    setIsLoading(true);
    setNetworkError(false);

    try {
      const response = await fetch('/api/auth/signin', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setUser(data.user);
        localStorage.setItem('user', JSON.stringify(data.user));
        localStorage.setItem('userId', data.user.id);

        // Update the lastLogin timestamp
        try {
          await fetch('/api/auth/update-login-time', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ userId: data.user.id }),
          });
          console.log('Last login timestamp updated');
        } catch (updateError) {
          console.error('Error updating last login timestamp:', updateError);
          // Continue even if the update fails
        }

        setIsLoading(false);
        router.push('/');
        return data;
      } else {
        setIsLoading(false);
        return {
          success: false,
          error: data.error || 'Sign in failed'
        };
      }
    } catch (error) {
      console.error('Sign in error:', error);
      setIsLoading(false);
      setNetworkError(true);
      return {
        success: false,
        error: 'Network error occurred'
      };
    }
  };

  // Sign up function
  const signUp = async (name: string, email: string, password: string) => {
    setIsLoading(true);
    setNetworkError(false);

    try {
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name, email, password }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // Set user state and store in localStorage
        setUser(data.user);
        localStorage.setItem('user', JSON.stringify(data.user));
        localStorage.setItem('userId', data.user.id);

        // The primary profile is now created automatically in the signup API
        setIsLoading(false);
        router.push('/');
        return { success: true };
      } else {
        setIsLoading(false);
        return {
          success: false,
          error: data.error || 'Sign up failed'
        };
      }
    } catch (error) {
      console.error('Sign up error:', error);
      setIsLoading(false);
      setNetworkError(true);
      return {
        success: false,
        error: 'Network error during sign up. Please check your connection and try again.'
      };
    }
  };

  // Google sign in function
  const googleSignIn = async (response: GoogleSignInResponse) => {
    setIsLoading(true);
    setNetworkError(false);

    try {
      // Validate the input data
      if (!response || !response.credential) {
        console.error('Invalid Google sign-in response received:', response);
        setIsLoading(false);
        return {
          success: false,
          error: 'Invalid Google authentication data received'
        };
      }

      console.log('Processing Google credential');

      // Process the Google profile data
      const googleProfile = handleGoogleSignIn(response);

      if (!googleProfile) {
        console.error('Failed to parse Google profile data');
        setIsLoading(false);
        return {
          success: false,
          error: 'Unable to process Google authentication data. Please try again.'
        };
      }

      // Log the profile data for debugging (limited info for privacy)
      console.log('Google profile data processed successfully:', {
        id: googleProfile.id ? googleProfile.id.substring(0, 5) + '...' : 'missing',
        email: googleProfile.email ? googleProfile.email.substring(0, 3) + '...' : 'missing',
        hasName: !!googleProfile.name,
        hasPicture: !!googleProfile.picture
      });

      // Validate required fields
      if (!googleProfile.id || !googleProfile.email) {
        console.error('Missing required Google profile fields');
        setIsLoading(false);
        return {
          success: false,
          error: 'Incomplete Google profile data. Please ensure your Google account has an email address.'
        };
      }

      // Send to the server
      try {
        // Prepare the data for the API
        const userData = {
          googleId: googleProfile.id,
          email: googleProfile.email,
          name: googleProfile.name || googleProfile.email.split('@')[0], // Fallback name if missing
          givenName: googleProfile.given_name,
          familyName: googleProfile.family_name,
          picture: googleProfile.picture
        };

        console.log('Sending Google profile to server:', {
          googleId: userData.googleId ? userData.googleId.substring(0, 5) + '...' : 'missing',
          email: userData.email ? userData.email.substring(0, 3) + '...' : 'missing',
        });

        const apiResponse = await fetch('/api/auth/google', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(userData),
        });

        if (!apiResponse.ok) {
          const errorText = await apiResponse.text();
          console.error(`Google sign-in API error (${apiResponse.status}):`, errorText);

          // Try to parse the error as JSON, but handle case where it's plain text
          let errorData;
          try {
            errorData = JSON.parse(errorText);
          } catch {
            errorData = { error: errorText || `Server error: ${apiResponse.status}` };
          }

          setIsLoading(false);
          return {
            success: false,
            error: errorData.error || 'Google sign in failed'
          };
        }

        const data = await apiResponse.json();

        if (data.success) {
          const user = data.user;

          // Set user state and store in localStorage
          setUser(user);
          localStorage.setItem('user', JSON.stringify(user));
          localStorage.setItem('userId', user.id);

          // Update the lastLogin timestamp
          try {
            await fetch('/api/auth/update-login-time', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ userId: user.id }),
            });
            console.log('Last login timestamp updated for Google sign-in');
          } catch (updateError) {
            console.error('Error updating last login timestamp for Google sign-in:', updateError);
            // Continue even if the update fails
          }

          // Ensure we have a picture URL to display
          if (user.picture) {
            // Google images sometimes use a parameter to limit size, remove it for best quality
            if (user.picture.includes('=s')) {
              const cleanPictureUrl = user.picture.split('=s')[0];
              console.log('Using full-resolution Google profile image:', cleanPictureUrl);
            }
          }

          // Only create a profile if this is a new user
          if (data.isNewUser) {
            // Create a primary profile for the user
            try {
              // Check how many profiles the user already has
              const profilesResponse = await fetch(`/api/profiles?userId=${user.id}`);
              const profilesData = await profilesResponse.json();

              // Only create a profile if they don't already have one
              if (!profilesData.profiles || profilesData.profiles.length === 0) {
                const profileResponse = await fetch('/api/profiles', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    userId: user.id,
                    name: user.name,
                    avatar: user.picture || `/avatars/avatar-${Math.floor(Math.random() * 6) + 1}.png`,
                    isKids: false,
                    isPrimary: true
                  })
                });

                if (!profileResponse.ok) {
                  console.error('Failed to create primary profile for Google user');
                } else {
                  console.log('Primary profile created for Google user');
                }
              } else {
                console.log('User already has profiles, skipping profile creation');
              }
            } catch (profileError) {
              console.error('Error creating profile for Google user:', profileError);
            }
          }

          setIsLoading(false);
          router.push('/');
          return { success: true };
        } else {
          console.error('Google sign-in API returned error:', data.error);
          setIsLoading(false);
          return {
            success: false,
            error: data.error || 'Google sign in failed'
          };
        }
      } catch (fetchError) {
        console.error('Network error during Google sign-in API call:', fetchError);
        setIsLoading(false);
        setNetworkError(true);
        return {
          success: false,
          error: 'Network error during Google sign in. Please check your connection and try again.'
        };
      }
    } catch (error) {
      console.error('Unexpected error during Google sign in:', error);
      setIsLoading(false);
      setNetworkError(true);
      return {
        success: false,
        error: 'An unexpected error occurred during Google sign in. Please try again.'
      };
    }
  };

  // Sign out function - updated to return a Promise with success/error status
  const handleSignOut = useCallback(async (): Promise<{ success: boolean; error?: string }> => {
    console.log('Starting sign out process');

    // Import the disconnectPusher function
    const { disconnectPusher } = await import('@/lib/pusher-client');

    try {
      // Disconnect Pusher before clearing user state
      disconnectPusher();
      console.log('Pusher disconnected');

      // Store user ID for logging
      const userId = user?.id;
      console.log('Signing out user:', userId);

      // Clear user state
      setUser(null);
      console.log('User state cleared');

      // Clear local storage
      localStorage.removeItem('user');
      localStorage.removeItem('userId');
      localStorage.removeItem('watchPartyUserId'); // Also clear watch party user ID
      localStorage.removeItem('activeProfile'); // Also clear active profile
      console.log('Local storage cleared');

      // Clear cookies by making a request to the server
      try {
        console.log('Sending sign-out request to server');
        const response = await fetch('/api/auth/signout', {
          method: 'POST',
          cache: 'no-store',
          credentials: 'include', // Ensure cookies are sent
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        const data = await response.json();
        console.log('Sign-out response:', data);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Error during sign out:', errorText);
          return { success: false, error: `Server error: ${errorText}` };
        } else {
          console.log('Server sign-out successful');
        }
      } catch (error) {
        console.error('Error clearing cookies:', error);
        // Continue with sign out even if cookie clearing fails
        // But track the error for reporting
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return {
          success: true,
          error: `Warning: Server cookie clearing failed (${errorMessage}), but local state was cleared`
        };
      }

      // Also try to clear cookies directly from client side
      // This is important for Netlify deployments where server-side cookie clearing might have issues
      try {
        // Clear cookies with all possible domain and path combinations
        const cookiesToClear = ['userId', 'session', 'next-auth.session-token', '__Secure-next-auth.session-token'];
        const paths = ['/', '/api', ''];
        const domain = window.location.hostname;

        cookiesToClear.forEach(cookieName => {
          // Clear with specific domain
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${domain};`;

          // Clear with each path variation
          paths.forEach(path => {
            document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path};`;
            document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; domain=${domain};`;
          });

          // Also try with secure and SameSite attributes
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax;`;
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax; Secure;`;
        });

        console.log('Attempted to clear cookies from client side with multiple variations');
      } catch (cookieError) {
        console.error('Error clearing cookies from client side:', cookieError);
      }

      // Return success
      return { success: true };
    } catch (error) {
      console.error('Error during sign out process:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return { success: false, error: `Sign out failed: ${errorMessage}` };
    }
  }, [setUser, user]);

  // Derived authentication state
  const isAuthenticated = (() => {
    const authState = user !== null;
    if (process.env.NODE_ENV === 'development') {
      console.log('Auth state:', { authState, userId: user?.id });
    }
    return authState;
  })();

  // Add the updateProfileImage function in the AuthProvider component
  // This updates the user's main profile image and the primary profile's avatar
  // Note: Each profile can have its own avatar, but the user's profile image
  // should match the primary profile's avatar for consistency
  const updateProfileImage = async (imageUrl: string) => {
    if (!user?.id) {
      return { success: false, error: 'Not authenticated' };
    }

    try {
      // Store the previous image URL for cleanup
      const previousImageUrl = user.picture;

      // Add cache-busting parameter to the image URL if it's a Cloudinary URL
      let cachebustedImageUrl = imageUrl;
      if (imageUrl.includes('cloudinary.com')) {
        // Add a timestamp parameter to prevent caching
        const separator = imageUrl.includes('?') ? '&' : '?';
        cachebustedImageUrl = `${imageUrl}${separator}t=${Date.now()}`;
      }

      // Update user profile image in MongoDB
      // This will also update the primary profile's avatar, but not other profiles
      const response = await fetch('/api/user/profile-image', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user.id,
          imageUrl: cachebustedImageUrl,
          previousImageUrl  // Send previous URL for cleanup
        }),
        cache: 'no-store',
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // Update local user state with new image URL
        setUser(prevUser => {
          if (!prevUser) return null;

          const updatedUser = {
            ...prevUser,
            picture: cachebustedImageUrl,
            profileImage: cachebustedImageUrl // Update both fields for consistency
          };

          // Update local storage as well
          localStorage.setItem('user', JSON.stringify(updatedUser));

          return updatedUser;
        });

        // Dispatch a custom event to notify other tabs/windows about the profile image change
        try {
          // Create and dispatch a custom event
          const profileImageChangeEvent = new CustomEvent('profile-image-changed', {
            detail: {
              userId: user.id,
              imageUrl: cachebustedImageUrl,
              timestamp: Date.now()
            }
          });
          window.dispatchEvent(profileImageChangeEvent);

          // Also use localStorage to communicate with other tabs
          localStorage.setItem('lastProfileImageUpdate', JSON.stringify({
            userId: user.id,
            imageUrl: cachebustedImageUrl,
            timestamp: Date.now()
          }));
        } catch (eventError) {
          console.error('Error dispatching profile image change event:', eventError);
        }

        // Update the active profile in local storage if it's the primary profile
        try {
          const storedProfile = localStorage.getItem('activeProfile');
          if (storedProfile) {
            const parsedProfile = JSON.parse(storedProfile);
            // Only update if this is the primary profile
            if (parsedProfile.userId === user.id && parsedProfile.isPrimary) {
              parsedProfile.avatar = cachebustedImageUrl;
              localStorage.setItem('activeProfile', JSON.stringify(parsedProfile));
            }
          }
        } catch (storageError) {
          console.error('Error updating profile in local storage:', storageError);
        }

        return { success: true };
      } else {
        console.error('Failed to update profile image:', data.error);
        return { success: false, error: data.error || 'Failed to update profile image' };
      }
    } catch (error) {
      console.error('Error updating profile image:', error);
      return { success: false, error: 'Error during profile image update' };
    }
  };

  // Add the updateUserName function
  const updateUserName = async (name: string) => {
    if (!user?.id) {
      return { success: false, error: 'Not authenticated' };
    }

    try {
      // Update user name in MongoDB
      const response = await fetch('/api/user/account', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user.id,
          name
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // Update local user state with new name
        setUser(prevUser => {
          if (!prevUser) return null;

          const updatedUser = {
            ...prevUser,
            name
          };

          // Update local storage as well
          localStorage.setItem('user', JSON.stringify(updatedUser));

          return updatedUser;
        });

        // Dispatch a custom event to notify other contexts about the name change
        // This will be used by ProfileContext to refresh profiles
        const nameChangeEvent = new CustomEvent('user-name-changed', {
          detail: { userId: user.id, name }
        });
        window.dispatchEvent(nameChangeEvent);

        return { success: true };
      } else {
        console.error('Failed to update user name:', data.error);
        return { success: false, error: data.error || 'Failed to update user name' };
      }
    } catch (error) {
      console.error('Error updating user name:', error);
      return { success: false, error: 'Error during user name update' };
    }
  };

  // Function to check if user is admin based on local state
  const isAdmin = () => {
    if (!user) return false;
    return user.role === 'admin' || user.role === 'superadmin';
  };

  // Function to verify admin status with the server
  const verifyAdminStatus = async (): Promise<boolean> => {
    try {
      // First check client-side admin status to avoid unnecessary server calls
      if (!user || !isAdmin()) {
        return false;
      }

      // Create a custom endpoint that doesn't rely on cookies
      // Instead, we'll pass the user ID in the query string
      const userId = user.id;
      const response = await fetch(`/api/admin/check-admin?userId=${userId}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const data = await response.json();
        return data.isAdmin === true;
      }

      // Log the error for debugging
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Admin verification failed:', errorData.message || 'Unknown error');
      }

      return false;
    } catch (error) {
      console.error('Error verifying admin status:', error);
      return false;
    }
  };

  // Value provided to consuming components
  const authContextValue: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    signIn,
    signUp,
    signOut: handleSignOut,
    googleSignIn,
    networkError,
    refreshAuthState,
    updateProfileImage,
    updateUserName,
    isAdmin,
    verifyAdminStatus,
  };

  return (
    <AuthContext.Provider value={authContextValue}>
      {children}
    </AuthContext.Provider>
  );
};