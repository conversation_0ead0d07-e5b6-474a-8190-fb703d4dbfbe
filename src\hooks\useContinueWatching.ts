'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { continueWatching as mockData } from '@/data/content';

// Define the watch item interface
export interface WatchItem {
  id: string | number;
  title: string;
  image: string;
  type: 'show' | 'movie';
  progress: number; // 0-100
  episode?: string;
  season?: number | string;
  timestamp?: string; // e.g. "35:42"
  currentTime?: number; // in seconds
  duration?: number; // in seconds
}

/**
 * Custom hook to fetch and manage continue watching data
 * Uses real user data when authenticated, falls back to mock data when not
 */
export function useContinueWatching() {
  const { user, isAuthenticated } = useAuth();
  const [items, setItems] = useState<WatchItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isMockData, setIsMockData] = useState(true);

  useEffect(() => {
    async function fetchContinueWatching() {
      setIsLoading(true);
      setError(null);
      
      try {
        // If user is not authenticated, use mock data
        if (!isAuthenticated || !user?.id) {
          setItems(mockData);
          setIsMockData(true);
          setIsLoading(false);
          return;
        }
        
        // Fetch real data from the API
        const response = await fetch(`/api/user/continue-watching?userId=${user.id}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch continue watching data');
        }
        
        const data = await response.json();
        
        if (data.success) {
          setItems(data.items);
          setIsMockData(data.isMockData);
        } else {
          throw new Error(data.error || 'Unknown error');
        }
      } catch (err) {
        console.error('Error fetching continue watching data:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setItems(mockData);
        setIsMockData(true);
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchContinueWatching();
  }, [isAuthenticated, user?.id]);
  
  return {
    items,
    isLoading,
    error,
    isMockData
  };
}
