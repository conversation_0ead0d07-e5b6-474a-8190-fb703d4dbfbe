/**
 * Geo-location utilities for visitor tracking
 *
 * This module provides functions to get location information from IP addresses.
 * It uses a free IP geolocation API for basic location data.
 */

/**
 * Get location information from an IP address
 *
 * @param ipAddress The IP address to look up
 * @returns Location information including country, city, and region
 */
export async function getLocationFromIp(ipAddress: string): Promise<{
  country?: string;
  countryCode?: string;
  region?: string;
  city?: string;
  timezone?: string;
  latitude?: number;
  longitude?: number;
}> {
  try {
    // Skip for localhost or private IPs
    if (
      !ipAddress ||
      ipAddress === 'localhost' ||
      ipAddress === '127.0.0.1' ||
      ipAddress === '::1' ||
      ipAddress.startsWith('192.168.') ||
      ipAddress.startsWith('10.') ||
      ipAddress.startsWith('172.16.') ||
      ipAddress === 'unknown'
    ) {
      console.log('Local or private IP detected:', ipAddress);
      return {
        country: 'Local',
        city: 'Development',
        region: 'Local Network',
        countryCode: 'LO',
        timezone: 'Local'
      };
    }

    console.log('Fetching location data for IP:', ipAddress);

    // Try multiple free IP geolocation APIs for better reliability
    // First try ipapi.co
    let response = await fetch(`https://ipapi.co/${ipAddress}/json/`, {
      headers: {
        'User-Agent': 'StreamVista/1.0'
      },
      // Add a timeout to prevent hanging requests
      signal: AbortSignal.timeout(3000) // 3 second timeout
    }).catch(err => {
      console.warn('First geolocation API failed:', err.message);
      return null;
    });

    // If first API fails, try a fallback API
    if (!response || !response.ok) {
      console.log('First geolocation API failed, trying fallback...');

      // Try ip-api.com as fallback
      response = await fetch(`http://ip-api.com/json/${ipAddress}`, {
        signal: AbortSignal.timeout(3000) // 3 second timeout
      }).catch(err => {
        console.warn('Fallback geolocation API failed:', err.message);
        return null;
      });
    }

    if (!response || !response.ok) {
      console.error(`Failed to get location data from all APIs`);
      return {
        country: 'Unknown',
        countryCode: 'XX',
        region: 'Unknown',
        city: 'Unknown',
        timezone: 'Unknown'
      };
    }

    const data = await response.json();

    // Check which API we got data from and parse accordingly
    let locationData;

    // Check for ipapi.co format
    if (data.country_name) {
      // This is ipapi.co format
      console.log('Using data from ipapi.co');

      // Check for error response
      if (data.error) {
        console.warn('IP geolocation error:', data.reason || 'Unknown reason');

        // If we hit rate limits, use a fallback
        if (data.reason && data.reason.includes('rate limit')) {
          console.log('Rate limit hit, using fallback location data');
          return {
            country: 'Unknown (Rate Limited)',
            countryCode: 'XX',
            region: 'Unknown',
            city: 'Unknown',
            timezone: 'Unknown'
          };
        }

        return {};
      }

      locationData = {
        country: data.country_name,
        countryCode: data.country_code,
        region: data.region,
        city: data.city,
        timezone: data.timezone,
        latitude: data.latitude,
        longitude: data.longitude
      };
    }
    // Check for ip-api.com format
    else if (data.country) {
      // This is ip-api.com format
      console.log('Using data from ip-api.com');

      // Check for error response
      if (data.status === 'fail') {
        console.warn('IP geolocation error:', data.message || 'Unknown reason');
        return {};
      }

      locationData = {
        country: data.country,
        countryCode: data.countryCode,
        region: data.regionName,
        city: data.city,
        timezone: data.timezone,
        latitude: data.lat,
        longitude: data.lon
      };
    }
    // Unknown format
    else {
      console.warn('Unknown API response format:', data);
      return {
        country: 'Unknown Format',
        countryCode: 'XX',
        region: 'Unknown',
        city: 'Unknown',
        timezone: 'Unknown'
      };
    }

    console.log('Successfully retrieved location data:', {
      country: locationData.country,
      city: locationData.city,
      region: locationData.region
    });

    return locationData;
  } catch (error) {
    console.error('Error getting location from IP:', error);

    // Return a fallback for error cases
    return {
      country: 'Error',
      countryCode: 'XX',
      region: 'Error',
      city: 'Error Retrieving Location',
      timezone: 'Unknown'
    };
  }
}

/**
 * Format location information for display
 *
 * @param country The country name
 * @param city The city name
 * @param region The region/state name
 * @returns Formatted location string
 */
export function formatLocation(country?: string, city?: string, region?: string): string {
  if (!country && !city && !region) {
    return 'Unknown';
  }

  const parts = [];

  if (city) parts.push(city);
  if (region && region !== city) parts.push(region);
  if (country) parts.push(country);

  return parts.join(', ');
}
