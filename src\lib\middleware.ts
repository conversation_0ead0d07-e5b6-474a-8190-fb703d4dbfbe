import { NextRequest, NextResponse } from 'next/server';
import User from '@/models/User'; // Static import
import { IUser } from '@/models/User'; // Import IUser interface
import { ensureMongooseConnection } from '@/lib/mongodb';

interface AdminCheckResult {
  isAdmin: boolean;
  user?: IUser;
  message?: string;
}

/**
 * Middleware to check if a user has admin privileges
 * This version uses cookie-based authentication
 */
export async function adminMiddleware(req: NextRequest): Promise<AdminCheckResult> {
  try {
    // Get the userId from cookies
    const userId = req.cookies.get('userId')?.value;

    // Log cookie information for debugging
    console.log('Admin middleware (lib): Cookies in request:', req.cookies.getAll().map(c => c.name));
    console.log('Admin middleware (lib): userId from cookie:', userId);
    console.log('Admin middleware (lib): Request URL:', req.url);

    // Check if the user is authenticated
    if (!userId) {
      console.log('Admin middleware (lib): No userId found in cookies');
      return { isAdmin: false, message: 'User not authenticated' };
    }

    // Check the database
    await ensureMongooseConnection();
    // Use statically imported User model

    // Find user by ID
    const user = await User.findById(userId);

    if (!user) {
      return { isAdmin: false, message: 'User not found in database' };
    }

    // Check if user has admin role
    const isAdmin = user.role === 'admin' || user.role === 'superadmin';

    return {
      isAdmin,
      user,
      message: isAdmin ? undefined : 'User does not have admin privileges'
    };
  } catch (error) {
    console.error('Error in admin middleware:', error);
    return { isAdmin: false, message: 'Error checking admin status' };
  }
}

/**
 * Middleware to authenticate user
 * This version uses cookie-based authentication
 */
export async function authMiddleware(req: NextRequest) {
  try {
    // Get the userId from cookies
    const userId = req.cookies.get('userId')?.value;

    // Log cookie information for debugging
    console.log('Auth middleware: Cookies in request:', req.cookies.getAll().map(c => c.name));
    console.log('Auth middleware: userId from cookie:', userId);
    console.log('Auth middleware: Request URL:', req.url);

    // Check if the user is authenticated
    if (!userId) {
      console.log('Auth middleware: No userId found in cookies');
      return { isAuthenticated: false, message: 'User not authenticated' };
    }

    // Check the database
    await ensureMongooseConnection();
    // Use statically imported User model

    // Find user by ID
    const user = await User.findById(userId);

    if (!user) {
      return { isAuthenticated: false, message: 'User not found in database' };
    }

    return { isAuthenticated: true, user };
  } catch (error) {
    console.error('Error in auth middleware:', error);
    return { isAuthenticated: false, message: 'Error checking authentication status' };
  }
}

// Check if user is admin
export async function isAdmin(request: NextRequest) {
  try {
    // Check for development bypass header
    const bypassHeader = request.headers.get('x-admin-bypass');
    if (process.env.NODE_ENV === 'development' && bypassHeader === 'true') {
      console.log('isAdmin middleware: Development bypass header detected, allowing access');
      return {
        isAuthorized: true,
        user: {
          role: 'admin',
          _id: 'dev-admin-id', // Add _id for TypeScript compatibility
          name: 'Development Admin',
          email: '<EMAIL>'
        }
      };
    }

    // Get the userId from cookies
    console.log('isAdmin middleware: Checking cookies for userId');
    const userId = request.cookies.get('userId')?.value;

    // Log all cookies for debugging
    console.log('isAdmin middleware: All cookies:', request.cookies.getAll().map(c => `${c.name}=${c.value.substring(0, 5)}...`));
    console.log('isAdmin middleware: Request URL:', request.url);
    console.log('isAdmin middleware: Environment:', process.env.NODE_ENV);

    if (!userId) {
      console.log('isAdmin middleware: No userId found in cookies');

      // In development, allow access even without a cookie
      if (process.env.NODE_ENV === 'development') {
        console.log('isAdmin middleware: Development mode - allowing access without cookie');
        return {
          isAuthorized: true,
          user: {
            role: 'admin',
            _id: 'dev-admin-id', // Add _id for TypeScript compatibility
            name: 'Development Admin',
            email: '<EMAIL>'
          }
        };
      }

      return {
        isAuthorized: false,
        message: 'No user ID found in cookies'
      };
    }

    // Connect to database
    await ensureMongooseConnection();

    // Find the user by ID
    const user = await User.findById(userId).lean();

    if (!user) {
      console.log(`isAdmin middleware: User not found for ID ${userId}`);

      // In development, allow access even if user not found
      if (process.env.NODE_ENV === 'development') {
        console.log('isAdmin middleware: Development mode - allowing access for non-existent user');
        return {
          isAuthorized: true,
          user: {
            role: 'admin',
            _id: 'dev-admin-id', // Add _id for TypeScript compatibility
            name: 'Development Admin',
            email: '<EMAIL>'
          }
        };
      }

      return {
        isAuthorized: false,
        message: 'User not found'
      };
    }

    // Check if user has admin role
    const isUserAdmin = user.role === 'admin' || user.role === 'superadmin';

    if (!isUserAdmin) {
      console.log(`isAdmin middleware: User ${userId} does not have admin role (role: ${user.role})`);

      // In development, allow access even if not admin
      if (process.env.NODE_ENV === 'development') {
        console.log('isAdmin middleware: Development mode - allowing access for non-admin user');
        return {
          isAuthorized: true,
          user: { ...user, role: 'admin' } // Override role for development
        };
      }

      return {
        isAuthorized: false,
        message: 'User does not have admin privileges'
      };
    }

    console.log(`isAdmin middleware: User ${userId} has admin privileges`);
    return {
      isAuthorized: true,
      user
    };
  } catch (error) {
    console.error('Error checking admin status:', error);

    // In development, allow access even if there's an error
    if (process.env.NODE_ENV === 'development') {
      console.log('isAdmin middleware: Development mode - allowing access despite error');
      return {
        isAuthorized: true,
        user: {
          role: 'admin',
          _id: 'dev-admin-id', // Add _id for TypeScript compatibility
          name: 'Development Admin',
          email: '<EMAIL>'
        }
      };
    }

    return {
      isAuthorized: false,
      message: 'Server error checking authorization'
    };
  }
}
