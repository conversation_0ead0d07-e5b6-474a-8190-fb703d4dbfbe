import { MongoClient, MongoClientOptions } from 'mongodb';
import mongoose, { ConnectOptions } from 'mongoose';

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

// Connection management - specific handling for Netlify
const isNetlify = process.env.NETLIFY === 'true' || process.env.CONTEXT === 'production' || process.env.CONTEXT === 'deploy-preview';
const MONGODB_URI = process.env.MONGODB_URI;
const MONGODB_DB = process.env.MONGODB_DB || 'streamvista';

// Only check for MONGODB_URI in server environment
if (!isBrowser && !MONGODB_URI) {
  throw new Error(
    'Please define the MONGODB_URI environment variable inside .env.local'
  );
}

// Connection options for MongoDB - optimized for serverless
const nativeOptions: MongoClientOptions = {
  connectTimeoutMS: isNetlify ? 10000 : 30000,
  socketTimeoutMS: isNetlify ? 20000 : 45000,
  maxPoolSize: isNetlify ? 10 : 20,
  minPoolSize: isNetlify ? 1 : 5,
  retryWrites: true,
  w: 'majority',
  heartbeatFrequencyMS: isNetlify ? 30000 : 10000,
  serverMonitoringMode: 'poll',
  // Add additional options to handle network errors
  serverSelectionTimeoutMS: isNetlify ? 15000 : 30000,
  waitQueueTimeoutMS: isNetlify ? 10000 : 20000,
};

// Logger for MongoDB connection events with severity levels
const logConnectionStatus = (event: string, details?: Record<string, unknown>, level: 'info' | 'warn' | 'error' = 'info') => {
  const message = `MongoDB | ${event}${details ? `: ${JSON.stringify(details)}` : ''}`;
  switch (level) {
    case 'warn': console.warn(message); break;
    case 'error': console.error(message); break;
    default: console.log(message);
  }
};

// --- Simplified Mongoose Connection ---
let mongooseConnectionPromise: Promise<typeof mongoose> | null = null;

async function ensureMongooseConnection(): Promise<typeof mongoose | null> {
  // Don't attempt to connect in browser environment
  if (isBrowser) {
    console.warn('MongoDB connection attempted in browser environment');
    return null;
  }

  // If connection is already established or connecting, return the existing instance
  if (mongoose.connection.readyState === 1) {
    logConnectionStatus('Using existing Mongoose connection (readyState 1)');
    return mongoose;
  }
  if (mongoose.connection.readyState === 2) {
    logConnectionStatus('Waiting for existing Mongoose connection attempt (readyState 2)');
    // If a connection attempt is in progress, wait for it
    if (mongooseConnectionPromise) {
      return mongooseConnectionPromise;
    }
    // Fall through if promise is null (shouldn't happen often)
  }

  // Reset promise if connection is in error state (readyState 0)
  if (mongoose.connection.readyState === 0 && mongooseConnectionPromise) {
    logConnectionStatus('Resetting stale connection promise', {}, 'warn');
    mongooseConnectionPromise = null;
  }

  // If no promise exists, start a new connection attempt
  if (!mongooseConnectionPromise) {
    logConnectionStatus('Creating new Mongoose connection');

    const mongooseOptions: ConnectOptions = {
      dbName: MONGODB_DB,
      autoIndex: !isNetlify,
      autoCreate: !isNetlify,
      serverSelectionTimeoutMS: isNetlify ? 10000 : 30000,
      socketTimeoutMS: isNetlify ? 20000 : 45000,
      connectTimeoutMS: isNetlify ? 10000 : 30000,
      writeConcern: { w: 'majority' },
      retryWrites: true,
      maxPoolSize: isNetlify ? 10 : 20,
      minPoolSize: isNetlify ? 1 : 5,
      heartbeatFrequencyMS: isNetlify ? 30000 : 10000,
      // Add additional settings to handle network errors
      bufferCommands: true,
    };

    // Set up event listeners *before* connecting
    mongoose.connection.once('connected', () => logConnectionStatus('Mongoose connected to MongoDB'));
    mongoose.connection.on('error', (err) => {
        // Check specific error codes
        const errorMessage = err?.message || String(err);
        if (errorMessage.includes('ECONNRESET')) {
            logConnectionStatus('Mongoose ECONNRESET error - connection reset by peer', {}, 'error');
        } else if (errorMessage.includes('ETIMEDOUT')) {
            logConnectionStatus('Mongoose connection timeout', {}, 'error');
        } else {
            console.error('Mongoose connection error:', err);
        }
        
        // Only reset promise for terminal errors
        if (!mongoose.connection.readyState) {
            mongooseConnectionPromise = null;
        }
    });
    mongoose.connection.on('disconnected', () => {
        logConnectionStatus('Mongoose disconnected');
        mongooseConnectionPromise = null; // Reset promise on disconnect
    });

    // Additional error handler for specific reconnect scenarios
    mongoose.connection.on('reconnectFailed', () => {
        logConnectionStatus('Mongoose reconnect attempts failed', {}, 'error');
        mongooseConnectionPromise = null;
    });

    mongooseConnectionPromise = mongoose.connect(MONGODB_URI!, mongooseOptions) // Add non-null assertion
      .then(mongooseInstance => {
        logConnectionStatus('Mongoose connection successful');
        return mongooseInstance;
      })
      .catch(error => {
        console.error('Mongoose connection failed:', error);
        mongooseConnectionPromise = null; // Ensure promise is reset on failure
        throw error; // Re-throw the error
      });
  }

  // Return the promise for the current connection attempt
  return mongooseConnectionPromise;
}

// --- Native Driver Connection (Kept for potential other uses, but simplified caching) ---
let nativeClientPromise: Promise<MongoClient> | null = null;

async function connectToDatabase(): Promise<MongoClient | null> {
  // Don't attempt to connect in browser environment
  if (isBrowser) {
    console.warn('MongoDB connection attempted in browser environment');
    return null;
  }

  if (!MONGODB_URI) {
    throw new Error('MONGODB_URI is not defined');
  }

  // Basic check if client exists and is connected (less robust than original)
  if (nativeClientPromise) {
      try {
          const client = await nativeClientPromise;
          // Simple ping check
          await client.db(MONGODB_DB).command({ ping: 1 });
          logConnectionStatus('Using existing native connection');
          return client;
      } catch (e) {
          logConnectionStatus('Existing native connection failed ping, creating new one', { error: (e as Error).message }, 'warn');
          nativeClientPromise = null; // Reset promise
      }
  }

  logConnectionStatus('Creating new native connection');
  const client = new MongoClient(MONGODB_URI, nativeOptions);
  nativeClientPromise = client.connect()
      .then(connectedClient => {
          logConnectionStatus('Native client connected successfully');
          return connectedClient;
      })
      .catch(error => {
          logConnectionStatus('Native client connection failed', { error: (error as Error).message }, 'error');
          nativeClientPromise = null; // Reset promise on error
          throw error;
      });

  return nativeClientPromise;
}


// --- Health Check and Reset (Simplified) ---
async function checkMongoHealth(): Promise<{status: string, details?: Record<string, unknown>}> {
  // Don't attempt to check health in browser environment
  if (isBrowser) {
    return { status: 'skipped', details: { message: 'MongoDB health check skipped in browser environment' } };
  }

  try {
    await ensureMongooseConnection(); // Try connecting
    const mongooseStatus = mongoose.connection.readyState === 1 ? 'connected' : 'disconnected';
    return { status: 'healthy', details: { mongoose: { status: mongooseStatus } } };
  } catch (error) {
    return { status: 'unhealthy', details: { error: (error as Error).message } };
  }
}

async function resetMongoConnections(): Promise<boolean> {
  // Don't attempt to reset connections in browser environment
  if (isBrowser) {
    console.warn('MongoDB reset connections attempted in browser environment');
    return false;
  }

  try {
    logConnectionStatus('Manually resetting MongoDB connections', {}, 'warn');
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.close();
    }
    mongooseConnectionPromise = null;
    // Also reset native client if needed
    if (nativeClientPromise) {
        const client = await nativeClientPromise.catch(() => null);
        if (client) await client.close();
        nativeClientPromise = null;
    }
    return true;
  } catch (error) {
    logConnectionStatus('Failed to reset connections', { error: (error as Error).message }, 'error');
    return false;
  }
}

// --- Exports ---
const clientPromise = isBrowser ? null : connectToDatabase(); // Keep native promise export if needed
const dbConnect = ensureMongooseConnection; // Alias for backward compatibility

export {
  connectToDatabase,
  ensureMongooseConnection,
  checkMongoHealth,
  resetMongoConnections,
  clientPromise,
  dbConnect
};

const mongodb = {
  connectToDatabase,
  ensureMongooseConnection,
  checkMongoHealth,
  resetMongoConnections,
  clientPromise,
  dbConnect
};

export default mongodb;
