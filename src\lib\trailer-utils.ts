/**
 * Utility functions for handling trailers
 */

interface TMDBVideo {
  id: string;
  key: string;
  name: string;
  site: string;
  size?: number;
  type: string;
  official: boolean;
  published_at?: string;
  iso_639_1?: string;
  iso_3166_1?: string;
}

interface TMDBVideosResponse {
  results: TMDBVideo[];
}

/**
 * Extracts YouTube trailer video ID from TMDB videos response
 * 
 * @param videos The videos array from TMDB API response (can be direct array or wrapped in results)
 * @returns The YouTube video ID or undefined if no trailer found
 */
export function extractTrailerFromTMDB(videos: TMDBVideosResponse | TMDBVideo[] | unknown): string | undefined {
  console.log('extractTrailerFromTMDB called with:', videos);
  
  let videosArray: TMDBVideo[] = [];
  
  // Handle different input formats
  if (Array.isArray(videos)) {
    // Direct array of videos
    videosArray = videos as TMDBVideo[];
  } else if (videos && typeof videos === 'object' && 'results' in videos) {
    // Wrapped in results object
    const videosData = videos as TMDBVideosResponse;
    if (Array.isArray(videosData.results)) {
      videosArray = videosData.results;
    }
  } else {
    console.log('extractTrailerFromTMDB: Invalid videos data format');
    return undefined;
  }
  
  if (videosArray.length === 0) {
    console.log('extractTrailerFromTMDB: No videos data or empty results');
    return undefined;
  }

  console.log(`extractTrailerFromTMDB: Processing ${videosArray.length} videos`);
  
  // Log all available videos for debugging
  videosArray.forEach((video, index) => {
    console.log(`Video ${index + 1}:`, {
      type: video.type,
      site: video.site,
      official: video.official,
      key: video.key,
      name: video.name
    });
  });

  // First, try to find an official trailer
  const officialTrailer = videosArray.find(
    (video: TMDBVideo) => 
      video.site === 'YouTube' && 
      video.type === 'Trailer' && 
      video.official === true &&
      video.key
  );

  if (officialTrailer) {
    console.log('extractTrailerFromTMDB: Found official trailer:', officialTrailer.key);
    return officialTrailer.key;
  }

  // If no official trailer, try to find any trailer
  const anyTrailer = videosArray.find(
    (video: TMDBVideo) => 
      video.site === 'YouTube' && 
      video.type === 'Trailer' &&
      video.key
  );

  if (anyTrailer) {
    console.log('extractTrailerFromTMDB: Found any trailer:', anyTrailer.key);
    return anyTrailer.key;
  }

  // If no trailer, try to find a teaser
  const teaser = videosArray.find(
    (video: TMDBVideo) => 
      video.site === 'YouTube' && 
      video.type === 'Teaser' &&
      video.key
  );

  if (teaser) {
    console.log('extractTrailerFromTMDB: Found teaser:', teaser.key);
    return teaser.key;
  }

  // If no trailer or teaser, just return the first YouTube video
  const anyYouTubeVideo = videosArray.find(
    (video: TMDBVideo) => 
      video.site === 'YouTube' &&
      video.key
  );

  if (anyYouTubeVideo) {
    console.log('extractTrailerFromTMDB: Found any YouTube video:', anyYouTubeVideo.key);
    return anyYouTubeVideo.key;
  }

  console.log('extractTrailerFromTMDB: No suitable video found');
  return undefined;
}

/**
 * Extracts YouTube video ID from a YouTube URL
 * 
 * @param url The YouTube URL
 * @returns The YouTube video ID or undefined if not a valid YouTube URL
 */
export function extractYouTubeId(url: string): string | undefined {
  if (!url) return undefined;

  // Match YouTube URL patterns
  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
  const match = url.match(regExp);

  return (match && match[2].length === 11)
    ? match[2]
    : undefined;
}

/**
 * Gets the YouTube thumbnail URL for a video ID
 * 
 * @param videoId The YouTube video ID
 * @param quality The thumbnail quality (default, medium, high, standard, maxres)
 * @returns The thumbnail URL
 */
export function getYouTubeThumbnail(
  videoId: string, 
  quality: 'default' | 'medium' | 'high' | 'standard' | 'maxres' = 'maxres'
): string {
  return `https://img.youtube.com/vi/${videoId}/${quality}default.jpg`;
}
