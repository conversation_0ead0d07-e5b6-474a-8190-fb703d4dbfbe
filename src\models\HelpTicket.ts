import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IHelpTicketResponse extends Document {
  _id: Types.ObjectId;
  ticketId: Types.ObjectId;
  responderId: Types.ObjectId;
  responderType: 'admin' | 'user';
  message: string;
  attachments?: string[];
  isInternal: boolean; // Internal notes only visible to admins
  createdAt: Date;
  updatedAt: Date;
}

export interface IHelpTicket extends Document {
  _id: Types.ObjectId;
  ticketNumber: string; // Auto-generated unique ticket number
  userId: Types.ObjectId;
  userEmail: string;
  userName: string;
  category: 'subscription' | 'technical' | 'billing' | 'content' | 'account' | 'bug_report' | 'feature_request' | 'other';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'waiting_for_user' | 'resolved' | 'closed';
  subject: string;
  description: string;
  attachments?: string[];
  assignedTo?: Types.ObjectId; // Admin user ID
  assignedToName?: string;
  tags: string[];
  metadata?: {
    userAgent?: string;
    browserInfo?: string;
    deviceInfo?: string;
    subscriptionStatus?: string;
    lastLoginDate?: Date;
    errorLogs?: string[];
    reproductionSteps?: string[];
  };
  responses: Types.ObjectId[];
  lastResponseAt?: Date;
  lastResponseBy?: Types.ObjectId;
  resolvedAt?: Date;
  resolvedBy?: Types.ObjectId;
  resolutionNotes?: string;
  satisfactionRating?: number; // 1-5 rating
  satisfactionFeedback?: string;
  escalated: boolean;
  escalatedAt?: Date;
  escalatedBy?: Types.ObjectId;
  escalationReason?: string;
  internalNotes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IHelpCategory extends Document {
  _id: Types.ObjectId;
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  isActive: boolean;
  sortOrder: number;
  parentCategory?: Types.ObjectId;
  subCategories: Types.ObjectId[];
  ticketCount: number;
  averageResolutionTime: number; // in hours
  createdAt: Date;
  updatedAt: Date;
}

// Help Ticket Response Schema
const HelpTicketResponseSchema = new Schema<IHelpTicketResponse>({
  ticketId: {
    type: Schema.Types.ObjectId,
    ref: 'HelpTicket',
    required: true,
    index: true
  },
  responderId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  responderType: {
    type: String,
    enum: ['admin', 'user'],
    required: true
  },
  message: {
    type: String,
    required: true,
    trim: true
  },
  attachments: [{
    type: String,
    trim: true
  }],
  isInternal: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true
});

// Help Ticket Schema
const HelpTicketSchema = new Schema<IHelpTicket>({
  ticketNumber: {
    type: String,
    required: false, // Will be generated in pre-save middleware
    unique: true,
    index: true
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  userEmail: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  userName: {
    type: String,
    required: true,
    trim: true
  },
  category: {
    type: String,
    enum: ['subscription', 'technical', 'billing', 'content', 'account', 'bug_report', 'feature_request', 'other'],
    required: true,
    index: true
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium',
    index: true
  },
  status: {
    type: String,
    enum: ['open', 'in_progress', 'waiting_for_user', 'resolved', 'closed'],
    default: 'open',
    index: true
  },
  subject: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  attachments: [{
    type: String,
    trim: true
  }],
  assignedTo: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    index: true
  },
  assignedToName: {
    type: String,
    trim: true
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  metadata: {
    userAgent: String,
    browserInfo: String,
    deviceInfo: String,
    subscriptionStatus: String,
    lastLoginDate: Date,
    errorLogs: [String],
    reproductionSteps: [String]
  },
  responses: [{
    type: Schema.Types.ObjectId,
    ref: 'HelpTicketResponse'
  }],
  lastResponseAt: {
    type: Date,
    index: true
  },
  lastResponseBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  resolvedAt: {
    type: Date,
    index: true
  },
  resolvedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  resolutionNotes: {
    type: String,
    trim: true
  },
  satisfactionRating: {
    type: Number,
    min: 1,
    max: 5
  },
  satisfactionFeedback: {
    type: String,
    trim: true
  },
  escalated: {
    type: Boolean,
    default: false,
    index: true
  },
  escalatedAt: Date,
  escalatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  escalationReason: {
    type: String,
    trim: true
  },
  internalNotes: {
    type: String,
    trim: true
  }
}, {
  timestamps: true
});

// Help Category Schema
const HelpCategorySchema = new Schema<IHelpCategory>({
  name: {
    type: String,
    required: true,
    trim: true
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  icon: {
    type: String,
    required: true,
    trim: true
  },
  color: {
    type: String,
    required: true,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  sortOrder: {
    type: Number,
    default: 0
  },
  parentCategory: {
    type: Schema.Types.ObjectId,
    ref: 'HelpCategory'
  },
  subCategories: [{
    type: Schema.Types.ObjectId,
    ref: 'HelpCategory'
  }],
  ticketCount: {
    type: Number,
    default: 0
  },
  averageResolutionTime: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Add indexes for better query performance
HelpTicketSchema.index({ createdAt: -1 });
HelpTicketSchema.index({ updatedAt: -1 });
HelpTicketSchema.index({ status: 1, priority: -1 });
HelpTicketSchema.index({ assignedTo: 1, status: 1 });
HelpTicketSchema.index({ category: 1, status: 1 });
HelpTicketSchema.index({ userEmail: 1, createdAt: -1 });
HelpTicketSchema.index({ escalated: 1, createdAt: -1 });

// Text search index
HelpTicketSchema.index({ 
  subject: 'text', 
  description: 'text', 
  ticketNumber: 'text',
  userEmail: 'text'
});

HelpTicketResponseSchema.index({ ticketId: 1, createdAt: 1 });
HelpTicketResponseSchema.index({ responderId: 1, createdAt: -1 });

HelpCategorySchema.index({ slug: 1 });
HelpCategorySchema.index({ isActive: 1, sortOrder: 1 });

// Pre-save middleware to generate ticket number (backup)
HelpTicketSchema.pre('save', async function(next) {
  if (this.isNew && !this.ticketNumber) {
    try {
      const timestamp = Date.now().toString(36).toUpperCase();
      const random = Math.random().toString(36).substring(2, 6).toUpperCase();
      this.ticketNumber = `SV-${timestamp}-${random}`;
    } catch (error) {
      console.error('Error generating ticket number:', error);
      return next(error as Error);
    }
  }
  next();
});

// Create models with proper error handling
let HelpTicket: mongoose.Model<IHelpTicket>;

try {
  // Check if model already exists
  HelpTicket = mongoose.models.HelpTicket as mongoose.Model<IHelpTicket>;
} catch (error) {
  // Model doesn't exist, create it
  HelpTicket = mongoose.model<IHelpTicket>('HelpTicket', HelpTicketSchema);
}

// If model still doesn't exist, create it
if (!HelpTicket) {
  HelpTicket = mongoose.model<IHelpTicket>('HelpTicket', HelpTicketSchema);
}

const HelpTicketResponse = mongoose.models.HelpTicketResponse as mongoose.Model<IHelpTicketResponse> || 
                          mongoose.model<IHelpTicketResponse>('HelpTicketResponse', HelpTicketResponseSchema);

const HelpCategory = mongoose.models.HelpCategory as mongoose.Model<IHelpCategory> || 
                    mongoose.model<IHelpCategory>('HelpCategory', HelpCategorySchema);

export { HelpTicket, HelpTicketResponse, HelpCategory };
export default HelpTicket;
