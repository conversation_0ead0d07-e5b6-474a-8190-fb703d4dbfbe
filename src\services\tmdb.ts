import { IContent } from '@/data/content';

const TMDB_API_KEY = process.env.NEXT_PUBLIC_TMDB_API_KEY;
const TMDB_ACCESS_TOKEN = process.env.NEXT_PUBLIC_TMDB_ACCESS_TOKEN;
const TMDB_BASE_URL = 'https://api.themoviedb.org/3';
const TMDB_IMAGE_BASE_URL = 'https://image.tmdb.org/t/p';

// Define TMDB response types
interface TMDBMovieResult {
  id: number;
  title: string;
  poster_path: string | null;
  backdrop_path: string | null;
  overview: string;
  release_date: string;
  genre_ids: number[];
  vote_average: number;
  runtime?: number;
}

interface TMDBTVResult {
  id: number;
  name: string;
  poster_path: string | null;
  backdrop_path: string | null;
  overview: string;
  first_air_date: string;
  genre_ids: number[];
  vote_average: number;
  number_of_seasons?: number;
  number_of_episodes?: number;
}

interface TMDBGenre {
  id: number;
  name: string;
}

interface TMDBResponse<T> {
  results: T[];
  page: number;
  total_pages: number;
  total_results: number;
}

interface TMDBVideo {
  id: string;
  key: string;
  name: string;
  site: string;
  size: number;
  type: string;
  official: boolean;
  published_at?: string;
  iso_639_1?: string;
  iso_3166_1?: string;
}

interface TMDBVideos {
  results: TMDBVideo[];
}

// Define proper interfaces for TMDB API responses
interface TMDBCastMember {
  id: number;
  name: string;
  character: string;
  profile_path: string | null;
  order: number;
}

interface TMDBCrewMember {
  id: number;
  name: string;
  job: string;
  department: string;
  profile_path: string | null;
}

interface TMDBCredits {
  cast: TMDBCastMember[];
  crew: TMDBCrewMember[];
}

interface TMDBContentItem {
  id: number;
  title?: string;
  name?: string;
  poster_path: string | null;
  backdrop_path: string | null;
  overview: string;
  vote_average: number;
  vote_count: number;
  release_date?: string;
  first_air_date?: string;
  genre_ids: number[];
  media_type?: 'movie' | 'tv';
}

interface TMDBContentResponse {
  results: TMDBContentItem[];
}

interface TMDBMovieDetails extends TMDBMovieResult {
  genres: TMDBGenre[];
  runtime: number;
  imdb_id: string;
  videos?: TMDBVideos;
  credits?: TMDBCredits;
  similar?: TMDBContentResponse;
  recommendations?: TMDBContentResponse;
}

interface TMDBTVDetails extends TMDBTVResult {
  genres: TMDBGenre[];
  number_of_seasons: number;
  number_of_episodes: number;
  videos?: TMDBVideos;
  credits?: TMDBCredits;
  similar?: TMDBContentResponse;
  recommendations?: TMDBContentResponse;
  external_ids?: {
    imdb_id?: string;
  };
}

// Define TMDB multi-search result item type (can include movies, tv, or people)
interface TMDBMultiResultItem {
  id: number;
  media_type: 'movie' | 'tv' | 'person';
  // Movie specific
  title?: string;
  release_date?: string;
  // TV specific
  name?: string;
  first_air_date?: string;
  // Common
  poster_path: string | null;
  backdrop_path: string | null;
  overview: string;
  genre_ids?: number[];
  vote_average: number;
  runtime?: number; // Typically only on details, but include if potentially available
  number_of_seasons?: number;
  number_of_episodes?: number;
}

// Map TMDB genre IDs to our genre names
const genreMap: Record<number, string> = {
  28: 'Action',
  12: 'Adventure',
  16: 'Animation',
  35: 'Comedy',
  80: 'Crime',
  99: 'Documentary',
  18: 'Drama',
  10751: 'Family',
  14: 'Fantasy',
  36: 'Historical',
  27: 'Horror',
  10402: 'Music',
  9648: 'Mystery',
  10749: 'Romance',
  878: 'Sci-Fi',
  10770: 'TV Movie',
  53: 'Thriller',
  10752: 'War',
  37: 'Western'
};

// Convert TMDB movie to our IContent format
function convertMovieToContent(movie: TMDBMovieResult): IContent {
  const year = movie.release_date ? movie.release_date.split('-')[0] : '';

  return {
    id: movie.id.toString(),
    title: movie.title,
    type: 'movie',
    year,
    posterPath: movie.poster_path ? `${TMDB_IMAGE_BASE_URL}/w500${movie.poster_path}` : '',
    backdropPath: movie.backdrop_path ? `${TMDB_IMAGE_BASE_URL}/original${movie.backdrop_path}` : '',
    overview: movie.overview,
    genres: movie.genre_ids?.map(id => genreMap[id]).filter(Boolean) || [],
    rating: movie.vote_average,
    runtime: movie.runtime,
    tmdbId: movie.id.toString()
  };
}

// Convert TMDB TV show to our IContent format
function convertTVToContent(show: TMDBTVResult): IContent {
  const year = show.first_air_date ? show.first_air_date.split('-')[0] : '';

  return {
    id: show.id.toString(),
    title: show.name,
    type: 'show',
    year,
    posterPath: show.poster_path ? `${TMDB_IMAGE_BASE_URL}/w500${show.poster_path}` : '',
    backdropPath: show.backdrop_path ? `${TMDB_IMAGE_BASE_URL}/original${show.backdrop_path}` : '',
    overview: show.overview,
    genres: show.genre_ids?.map(id => genreMap[id]).filter(Boolean) || [],
    rating: show.vote_average,
    seasons: show.number_of_seasons,
    episodes: show.number_of_episodes,
    tmdbId: show.id.toString()
  };
}

// API fetch utility
async function fetchFromTMDB<T>(endpoint: string, params: Record<string, string> = {}): Promise<T> {
  if (!TMDB_ACCESS_TOKEN) {
    console.error('TMDB Access Token not found');
    throw new Error('TMDB Access Token not found');
  }

  const url = new URL(`${TMDB_BASE_URL}${endpoint}`);

  // Add params to URL except the API key (using Authorization header instead)
  Object.entries(params).forEach(([key, value]) => {
    url.searchParams.append(key, value);
  });

  // Log the URL without exposing the access token
  const safeUrl = new URL(url.toString());
  console.log(`Fetching from TMDB: ${safeUrl.pathname}${safeUrl.search}`);

  try {
    const response = await fetch(url.toString(), {
      headers: {
        Authorization: `Bearer ${TMDB_ACCESS_TOKEN}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch (e) {
        errorData = { status_message: errorText };
      }

      const errorMessage = errorData?.status_message || `TMDB API error: ${response.status}`;
      // Log error without exposing the access token
      const safeUrl = new URL(url.toString());
      console.error('TMDB API Error:', {
        status: response.status,
        statusText: response.statusText,
        endpoint: `${safeUrl.pathname}${safeUrl.search}`,
        errorData
      });
      throw new Error(errorMessage);
    }

    const data = await response.json();
    console.log(`TMDB API success for ${endpoint}`, { resultCount: Array.isArray(data?.results) ? data.results.length : 'N/A' });
    return data;
  } catch (error) {
    console.error('Error fetching from TMDB:', error);
    throw error;
  }
}

// API functions
export async function getPopularMovies(page: number = 1): Promise<IContent[]> {
  const data = await fetchFromTMDB<TMDBResponse<TMDBMovieResult>>(
    '/movie/popular',
    { language: 'en-US', page: page.toString() }
  );

  return data.results.map(convertMovieToContent);
}

export async function getPopularShows(page: number = 1): Promise<IContent[]> {
  const data = await fetchFromTMDB<TMDBResponse<TMDBTVResult>>(
    '/tv/popular',
    { language: 'en-US', page: page.toString() }
  );

  return data.results.map(convertTVToContent);
}

export async function getTopRatedMovies(page: number = 1): Promise<IContent[]> {
  const data = await fetchFromTMDB<TMDBResponse<TMDBMovieResult>>(
    '/movie/top_rated',
    { language: 'en-US', page: page.toString() }
  );

  return data.results.map(convertMovieToContent);
}

export async function getTopRatedShows(page: number = 1): Promise<IContent[]> {
  const data = await fetchFromTMDB<TMDBResponse<TMDBTVResult>>(
    '/tv/top_rated',
    { language: 'en-US', page: page.toString() }
  );

  return data.results.map(convertTVToContent);
}

export async function searchContent(query: string, page: number = 1): Promise<{
  movies: IContent[];
  shows: IContent[];
}> {
  // Use the /search/multi endpoint
  const data = await fetchFromTMDB<TMDBResponse<TMDBMultiResultItem>>(
    '/search/multi',
    { query, language: 'en-US', page: page.toString(), include_adult: 'false' }
  );

  const movies: IContent[] = [];
  const shows: IContent[] = [];

  // Iterate through results and separate by media_type
  data.results.forEach(item => {
    if (item.media_type === 'movie') {
      // Map movie item to IContent
      // Need to adapt the structure from TMDBMultiResultItem to TMDBMovieResult expected by convertMovieToContent
      const movieItem: TMDBMovieResult = {
          id: item.id,
          title: item.title || 'Unknown Title',
          poster_path: item.poster_path,
          backdrop_path: item.backdrop_path,
          overview: item.overview,
          release_date: item.release_date || '',
          genre_ids: item.genre_ids || [],
          vote_average: item.vote_average,
          runtime: item.runtime // Pass runtime if available
      };
      movies.push(convertMovieToContent(movieItem));
    } else if (item.media_type === 'tv') {
      // Map TV item to IContent
      // Adapt structure from TMDBMultiResultItem to TMDBTVResult expected by convertTVToContent
       const tvItem: TMDBTVResult = {
          id: item.id,
          name: item.name || 'Unknown Show',
          poster_path: item.poster_path,
          backdrop_path: item.backdrop_path,
          overview: item.overview,
          first_air_date: item.first_air_date || '',
          genre_ids: item.genre_ids || [],
          vote_average: item.vote_average,
          number_of_seasons: item.number_of_seasons, // Pass season/episode info if available
          number_of_episodes: item.number_of_episodes
       };
      shows.push(convertTVToContent(tvItem));
    }
    // Ignore items with media_type === 'person' for now
  });

  return {
    movies,
    shows
  };
}

export async function getMovieDetails(id: string): Promise<IContent> {
  // Fetch movie details with videos
  const data = await fetchFromTMDB<TMDBMovieDetails>(`/movie/${id}`, {
    language: 'en-US',
    append_to_response: 'videos,credits,similar,recommendations'
  });

  console.log(`Fetched movie details for ID ${id}:`, {
    title: data.title,
    hasVideos: !!data.videos,
    videoCount: data.videos?.results?.length || 0
  });

  const content = convertMovieToContent(data);
  content.genres = data.genres.map(g => g.name);
  content.imdbId = data.imdb_id;
  content.runtime = data.runtime;

  // Add videos to the content
  if (data.videos) {
    content.videos = data.videos;
  }

  // Add credits (cast and crew) data
  if (data.credits) {
    content.credits = data.credits;
  }

  // Add similar and recommendations
  if (data.similar) {
    content.similar = data.similar;
  }

  if (data.recommendations) {
    content.recommendations = data.recommendations;
  }

  return content;
}

export async function getTVDetails(id: string): Promise<IContent> {
  // Fetch TV details with videos and other related data
  const data = await fetchFromTMDB<TMDBTVDetails>(`/tv/${id}`, {
    language: 'en-US',
    append_to_response: 'videos,credits,similar,recommendations,external_ids'
  });

  console.log(`Fetched TV details for ID ${id}:`, {
    title: data.name,
    hasVideos: !!data.videos,
    videoCount: data.videos?.results?.length || 0,
    hasExternalIds: !!data.external_ids
  });

  const content = convertTVToContent(data);
  content.genres = data.genres.map(g => g.name);
  content.seasons = data.number_of_seasons;
  content.episodes = data.number_of_episodes;

  // Add IMDb ID from external IDs
  if (data.external_ids && data.external_ids.imdb_id) {
    content.imdbId = data.external_ids.imdb_id;
    console.log(`Added IMDb ID ${data.external_ids.imdb_id} for TV show ${content.title}`);
  }

  // Add videos to the content
  if (data.videos) {
    content.videos = data.videos;
  }

  // Add credits (cast and crew) data
  if (data.credits) {
    content.credits = data.credits;
  }

  // Add similar and recommendations
  if (data.similar) {
    content.similar = data.similar;
  }

  if (data.recommendations) {
    content.recommendations = data.recommendations;
  }

  return content;
}

// Create TMDB content data for use in components
export async function getInitialContentData(): Promise<{
  popularMovies: IContent[];
  popularShows: IContent[];
  topRatedMovies: IContent[];
  topRatedShows: IContent[];
}> {
  try {
    const [
      popularMoviesData,
      popularShowsData,
      topRatedMoviesData,
      topRatedShowsData
    ] = await Promise.all([
      getPopularMovies(),
      getPopularShows(),
      getTopRatedMovies(),
      getTopRatedShows()
    ]);

    return {
      popularMovies: popularMoviesData,
      popularShows: popularShowsData,
      topRatedMovies: topRatedMoviesData,
      topRatedShows: topRatedShowsData
    };
  } catch (error) {
    console.error('Error getting initial content data:', error);
    return {
      popularMovies: [],
      popularShows: [],
      topRatedMovies: [],
      topRatedShows: []
    };
  }
}

/**
 * Search for a movie by IMDb ID
 */
export async function getMovieByImdbId(imdbId: string): Promise<IContent | null> {
  try {
    // Use the find endpoint to search by external ID
    const response = await fetchFromTMDB<{
      movie_results: TMDBMovieResult[];
    }>(`/find/${imdbId}`, {
      external_source: 'imdb_id',
      language: 'en-US'
    });

    // Check if we found any movies
    if (response.movie_results && response.movie_results.length > 0) {
      const movie = response.movie_results[0];
      // Get the full movie details using the TMDb ID we just found
      return await getMovieDetails(movie.id.toString());
    }

    return null;
  } catch (error) {
    console.error('Error fetching movie by IMDb ID:', error);
    return null;
  }
}

/**
 * Search for a TV show by IMDb ID
 */
export async function getTVByImdbId(imdbId: string): Promise<IContent | null> {
  try {
    // Use the find endpoint to search by external ID
    const response = await fetchFromTMDB<{
      tv_results: TMDBTVResult[];
    }>(`/find/${imdbId}`, {
      external_source: 'imdb_id',
      language: 'en-US'
    });

    // Check if we found any TV shows
    if (response.tv_results && response.tv_results.length > 0) {
      const show = response.tv_results[0];
      // Get the full TV details using the TMDb ID we just found
      return await getTVDetails(show.id.toString());
    }

    return null;
  } catch (error) {
    console.error('Error fetching TV show by IMDb ID:', error);
    return null;
  }
}

export async function getMovieRecommendations(id: string, page: number = 1): Promise<IContent[]> {
  const data = await fetchFromTMDB<TMDBResponse<TMDBMovieResult>>(
    `/movie/${id}/recommendations`,
    { language: 'en-US', page: page.toString() }
  );

  return data.results.map(convertMovieToContent);
}

export async function getTVRecommendations(id: string, page: number = 1): Promise<IContent[]> {
  const data = await fetchFromTMDB<TMDBResponse<TMDBTVResult>>(
    `/tv/${id}/recommendations`,
    { language: 'en-US', page: page.toString() }
  );

  return data.results.map(convertTVToContent);
}