// TMDB image base URL - defined here to avoid circular dependencies
const TMDB_IMAGE_BASE_URL = 'https://image.tmdb.org/t/p';

/**
 * Ensures a TMDB image path has the full URL.
 * If the path doesn't start with 'http', it prepends the TMDB base URL.
 *
 * @param path The image path to process
 * @param size The size to use for poster images ('w500', 'original', etc.)
 * @returns The full image URL
 */
export function ensureTMDBImagePath(path: string | null | undefined, size: string = 'w500'): string {
  if (!path) return '';

  // Log the incoming path for debugging
  console.log(`[IMAGE UTILS] Processing image path: "${path}", size: ${size}`);

  // If path is already a full URL, return it
  if (path.startsWith('http')) {
    console.log(`[IMAGE UTILS] Path is already a full URL: ${path}`);
    return path;
  }

  // Determine if this is a poster or backdrop by checking path
  const isBackdrop = path.includes('backdrop_path') || path.includes('backdrop');
  const imageSize = isBackdrop ? 'original' : size;

  // Ensure path starts with a slash
  const formattedPath = path.startsWith('/') ? path : `/${path}`;

  // Construct the full URL
  const fullUrl = `${TMDB_IMAGE_BASE_URL}/${imageSize}${formattedPath}`;
  console.log(`[IMAGE UTILS] Converted path to: ${fullUrl}`);

  return fullUrl;
}

/**
 * Ensures both poster and backdrop paths have full URLs.
 * Use this function to update content objects with proper image paths.
 *
 * @param content The content object with image paths
 * @returns A new content object with updated image paths
 */
export function ensureContentImagePaths<T extends { posterPath?: string | null, backdropPath?: string | null }>(content: T): T {
  if (!content) return content;

  console.log(`[IMAGE UTILS] Processing content: ${(content as any).title || 'Unknown'}`);

  const result = { ...content };

  if (content.posterPath) {
    // Process poster path
    const originalPosterPath = content.posterPath;
    result.posterPath = ensureTMDBImagePath(content.posterPath);
    console.log(`[IMAGE UTILS] Poster path: "${originalPosterPath}" -> "${result.posterPath}"`);
  } else {
    console.log(`[IMAGE UTILS] No poster path found for content`);
  }

  if (content.backdropPath) {
    // Process backdrop path
    const originalBackdropPath = content.backdropPath;
    result.backdropPath = ensureTMDBImagePath(content.backdropPath, 'original');
    console.log(`[IMAGE UTILS] Backdrop path: "${originalBackdropPath}" -> "${result.backdropPath}"`);
  }

  return result;
}